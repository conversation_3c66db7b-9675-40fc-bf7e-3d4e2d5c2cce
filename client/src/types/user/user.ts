export interface User {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: 'USER' | 'ADMIN';
    twoFactorEnabled: boolean;
    createdAt: string;
    updatedAt: string;
    preferredCommMethod: 'EMAIL' | 'SMS'
}

export interface CreateUserData {
    password: string;
    confirmPassword: string;
    firstName: string;
    middleName?: string;
    lastName: string;
    title?: string;
    birthDate: Date;
    gender: 'male' | 'female' | 'other';
    email: string;
    phone: string;
    address1: string;
    address2?: string;
    city: string;
    state: USState;
    acceptTerms: boolean;
}

type USState =
    | "AL" // Alabama
    | "AK" // Alaska
    | "AZ" // Arizona
    | "AR" // Arkansas
    | "CA" // California
    | "CO" // Colorado
    | "CT" // Connecticut
    | "DE" // Delaware
    | "FL" // Florida
    | "GA" // Georgia
    | "HI" // Hawaii
    | "ID" // Idaho
    | "IL" // Illinois
    | "IN" // Indiana
    | "IA" // Iowa
    | "KS" // Kansas
    | "KY" // Kentucky
    | "LA" // Louisiana
    | "ME" // Maine
    | "MD" // Maryland
    | "MA" // Massachusetts
    | "MI" // Michigan
    | "MN" // Minnesota
    | "MS" // Mississippi
    | "MO" // Missouri
    | "MT" // Montana
    | "NE" // Nebraska
    | "NV" // Nevada
    | "NH" // New Hampshire
    | "NJ" // New Jersey
    | "NM" // New Mexico
    | "NY" // New York
    | "NC" // North Carolina
    | "ND" // North Dakota
    | "OH" // Ohio
    | "OK" // Oklahoma
    | "OR" // Oregon
    | "PA" // Pennsylvania
    | "RI" // Rhode Island
    | "SC" // South Carolina
    | "SD" // South Dakota
    | "TN" // Tennessee
    | "TX" // Texas
    | "UT" // Utah
    | "VT" // Vermont
    | "VA" // Virginia
    | "WA" // Washington
    | "WV" // West Virginia
    | "WI" // Wisconsin
    | "WY" // Wyoming
    | "DC"; // District of Columbia