import React from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {LayoutDashboard, Book, Layers, Salad, ChartColumnBig, Calendar, File, Settings} from "lucide-react";
import {tokenStorage} from "../../../features/auth/api/api.ts";
import {AuthUser} from "../../../features/auth/types/authUser.ts";
import {useLogout} from "../../../features/auth/hooks/useAuth.ts";
import {confirmDialog} from "primereact/confirmdialog";
import {Tooltip} from "primereact/tooltip";
import {useUnsavedChangesStore} from "../../../stores/useUnsavedChangesStore.ts";


interface NavItem {
    path: string;
    label: string;
    icon: React.ReactNode;
}

const NavBar = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const user: AuthUser | null = tokenStorage.getUser();
    const logoutMutation = useLogout();

    // Zustand store for unsaved changes
    const checkUnsavedChanges = useUnsavedChangesStore(state => state.checkUnsavedChanges);

    const navItems: NavItem[] = [
        {
            path: '/dashboard',
            label: 'Dashboard',
            icon: (
                <LayoutDashboard/>
            )
        },
        {
            path: '/library',
            label: 'Resource Library',
            icon: (
                <Book/>
            )
        },
        {
            path: '/goals',
            label: 'Health Goals',
            icon: (
                <Layers/>
            )
        },
        {
            path: '/nutrition',
            label: 'Nutrition Plan',
            icon: (
                <Salad/>
            )
        },
        {
            path: '/lab-summary',
            label: 'Lab Summary',
            icon: (
                <ChartColumnBig/>
            )
        },
        {
            path: '/appointments',
            label: 'Lab Appointments',
            icon: (
                <Calendar/>
            )
        },
        {
            path: '/documents',
            label: 'Documents',
            icon: (
                <File/>
            )
        },
        {
            path: '/settings',
            label: 'Settings',
            icon: (
                <Settings/>
            )
        }
    ];

    const isActive = (path: string) => {
        if (path === '/dashboard' && location.pathname === '/') return true;
        return location.pathname === path;
    };

    const handleNavigation = (path: string) => {
        // Don't navigate if already on the page
        if (isActive(path)) return;

        checkUnsavedChanges(() => {
            navigate(path);
        });
    };

    const handleProfileClick = () => {
        checkUnsavedChanges(() => {
            navigate('user-profile');
        });
    };

    const confirmLogout = () => {
        checkUnsavedChanges(() => {
            confirmDialog({
                message: 'Are you sure you want to logout?',
                header: 'Confirm Logout',
                icon: 'pi pi-sign-out',
                defaultFocus: 'reject',
                acceptClassName: 'p-button-danger',
                accept: () => {
                    logoutMutation.mutate();
                },
                reject: () => {
                    console.log('Logout cancelled');
                }
            });
        });
    };

    return (
        <nav className="min-w-70 bg-white p-6 flex flex-col border-r border-gray-200 shadow-md">
            <div className="flex items-center gap-3 mb-10 font-bold text-xl text-gray-900">
                <div className="grid grid-cols-2 gap-0.5 w-8 h-8">
                    <div className="bg-gradient-to-br from-blue-500 to-blue-800 rounded-sm"></div>
                    <div className="bg-gradient-to-br from-blue-500 to-blue-800 rounded-sm"></div>
                    <div className="bg-gradient-to-br from-blue-500 to-blue-800 rounded-sm"></div>
                    <div className="bg-gradient-to-br from-blue-500 to-blue-800 rounded-sm"></div>
                </div>
                Noura Health
            </div>

            <div className="flex flex-col space-y-1">
                {navItems.map((item) => (
                    <div
                        key={item.path}
                        onClick={() => handleNavigation(item.path)}
                        className={`flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer ${
                            isActive(item.path)
                                ? 'bg-gradient-to-r from-blue-500 to-blue-700 text-white shadow-lg shadow-blue-500/30'
                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }`}
                    >
                        <div className="w-5 h-5 flex items-center justify-center">
                            {item.icon}
                        </div>
                        {item.label}
                    </div>
                ))}
            </div>

            <div className="mt-auto flex items-center gap-3 p-4 bg-gray-50 rounded-xl border border-gray-200">
                <Tooltip target=".user-profile-picture"/>
                <div
                    data-pr-tooltip="Go Profile"
                    data-pr-position="top"
                    data-pr-at="right-20 top-10"
                    onClick={handleProfileClick}
                    className="user-profile-picture w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-800 flex items-center justify-center text-white font-semibold text-base cursor-pointer"
                >
                    {[...user!.firstName][0]} {[...user!.lastName][0]}
                </div>
                <div className='flex flex-col flex-1'>
                    <div className="font-semibold text-gray-900 text-sm"> {user?.fullName}</div>
                    <div className="text-xs text-gray-600">Patient ID: {user?.id}</div>
                    <div className="flex text-xs text-gray-600 justify-end">
                        <span
                            className={`cursor-pointer font-medium transition-colors duration-200 ${
                                logoutMutation.isPending
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-blue-600 hover:text-blue-800'
                            }`}
                            onClick={logoutMutation.isPending ? undefined : confirmLogout}
                        >
                            {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
                        </span>
                    </div>
                </div>
            </div>
        </nav>
    );
};

export default NavBar;