import apiClient from "../../auth/api/api";
import {
    HealthWizardDataResponse,
    HealthWizardSubmitRequest,
    HealthWizardSubmitResponse,
} from "../types/wizardData.ts";


export const healthWizardService = {

    getWizardData: async (version?: number): Promise<HealthWizardDataResponse> => {
        let url = '/goal/data';
        const params = new URLSearchParams();

        if (version !== undefined) params.append('version', version.toString());
        if (params.toString()) url += `?${params.toString()}`;

        const response = await apiClient.get(url);
        return response.data;
    },

    getMaxVersion: async (): Promise<number> => {
        const response = await apiClient.get(`/goal/versions`);
        return response.data;
    },

    submitWizardData: async (data: HealthWizardSubmitRequest): Promise<HealthWizardSubmitResponse> => {
        const response = await apiClient.post('/goal/submit', data);
        return response.data;
    },
};