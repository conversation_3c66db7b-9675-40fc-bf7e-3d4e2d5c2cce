import { useState, useEffect } from 'react';
import { Card } from 'primereact/card';
import { Calendar } from 'primereact/calendar';
import { Button } from 'primereact/button';
import { Checkbox } from 'primereact/checkbox';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { useParams, useNavigate } from 'react-router-dom';
import {HeaderProps} from "../../../../types/layout/Header.ts";
import {toastService} from "../../../../services/utils/toastService.ts";

// Types
interface LabLocation {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    distance: number;
    coordinates: {
        lat: number;
        lng: number;
    };
}

interface TimeSlot {
    id: string;
    time: string;
    available: boolean;
}

interface Test {
    id: string;
    name: string;
    code: string;
}

interface LabScheduleProps extends HeaderProps {
    onSchedule?: (appointment: {
        lab: LabLocation;
        date: Date | null;
        time: string;
        tests: Test[]; // Changed to array
    }) => void;
}

export const LabSchedule = ({ setHeader, onSchedule }: LabScheduleProps) => {
    const { labId } = useParams<{ labId: string }>();
    const navigate = useNavigate();

    // Same mock data as LabAppointments
    const labLocations: LabLocation[] = [
        {
            id: '1',
            name: 'Quest Diagnostics',
            address: '555 Sycamore St',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43215',
            distance: 1.7,
            coordinates: { lat: 39.9712, lng: -82.9888 }
        },
        {
            id: '2',
            name: 'LabCorp',
            address: '466 Livingston Ave',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43215',
            distance: 3.2,
            coordinates: { lat: 39.9512, lng: -82.9788 }
        },
        {
            id: '3',
            name: 'BioReference Laboratories',
            address: '11 Park Pl',
            city: 'Upper Arlington',
            state: 'OH',
            zipCode: '43221',
            distance: 6.8,
            coordinates: { lat: 40.0012, lng: -83.0588 }
        },
        {
            id: '4',
            name: 'Sonic Healthcare',
            address: '500 Polaris Pkwy, Suite 100',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43082',
            distance: 11.0,
            coordinates: { lat: 40.1512, lng: -82.9988 }
        },
        {
            id: '5',
            name: 'ARUP Laboratories',
            address: '789 Medical Center Dr',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43214',
            distance: 4.5,
            coordinates: { lat: 39.9812, lng: -82.9688 }
        },
        {
            id: '6',
            name: 'Mayo Clinic Laboratories',
            address: '321 Healthcare Blvd',
            city: 'Dublin',
            state: 'OH',
            zipCode: '43017',
            distance: 8.3,
            coordinates: { lat: 40.0912, lng: -83.1188 }
        }
    ];

    const selectedLab = labLocations.find(lab => lab.id === labId);

    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [selectedTime, setSelectedTime] = useState<string>('');
    const [selectedTests, setSelectedTests] = useState<string[]>([]); // Changed to array
    const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
    const [existingAppointment, setExistingAppointment] = useState<any>(null);

    // Available time slots for weekdays
    const weekdayTimeSlots: TimeSlot[] = [
        { id: '1', time: '10:00 am', available: true },
        { id: '2', time: '10:30 am', available: true },
        { id: '3', time: '11:15 am', available: true },
        { id: '4', time: '12:00 pm', available: true },
        { id: '5', time: '1:15 pm', available: true },
        { id: '6', time: '3:00 pm', available: true },
        { id: '7', time: '4:45 pm', available: true }
    ];

    // Available time slots for Saturdays
    const saturdayTimeSlots: TimeSlot[] = [
        { id: '1', time: '9:00 am', available: true },
        { id: '2', time: '11:00 am', available: true }
    ];

    // Determine which time slots to show based on selected date
    const isSaturday = selectedDate?.getDay() === 6;
    const timeSlots = isSaturday ? saturdayTimeSlots : weekdayTimeSlots;

    // Recommended tests
    const recommendedTests: Test[] = [
        { id: '1', name: 'Complete Blood Count (CBC)', code: 'CBC' },
        { id: '2', name: 'Anti-Nuclear Antibodies (ANA)', code: 'ANA' },
        { id: '3', name: 'Iron Panel', code: 'IRON' },
        { id: '4', name: 'Vitamin B12', code: 'B12' },
        { id: '5', name: 'Lipid Panel', code: 'LIPID' },
        { id: '6', name: 'Comprehensive Metabolic Panel (CMP)', code: 'CMP' },
        { id: '7', name: 'Thyroid Stimulating Hormone (TSH)', code: 'TSH' }
    ];

    useEffect(() => {
        setHeader({
            title: 'Schedule Lab Appointment',
            subtitle: 'Choose date, time, and tests',
            icon: (
                <svg viewBox="0 0 24 24" className="w-4 h-4 stroke-current fill-none stroke-2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                    <line x1="16" y1="2" x2="16" y2="6"/>
                    <line x1="8" y1="2" x2="8" y2="6"/>
                    <line x1="3" y1="10" x2="21" y2="10"/>
                </svg>
            )
        });
    }, [setHeader]);

    // Check for existing appointments in localStorage
    const checkExistingAppointment = () => {
        try {
            const savedAppointments = localStorage.getItem('labAppointments');
            if (savedAppointments) {
                const appointments = JSON.parse(savedAppointments);
                if (appointments && appointments.length > 0) {
                    return appointments[0];
                }
            }
        } catch (error) {
            console.error('Error reading appointments:', error);
        }
        return null;
    };

    // Handle test selection toggle
    const handleTestToggle = (testId: string) => {
        setSelectedTests(prev => {
            if (prev.includes(testId)) {
                return prev.filter(id => id !== testId);
            } else {
                return [...prev, testId];
            }
        });
    };

    // Handle select all toggle
    const handleSelectAll = () => {
        if (selectedTests.length === recommendedTests.length) {
            // If all are selected, deselect all
            setSelectedTests([]);
        } else {
            // Select all
            setSelectedTests(recommendedTests.map(test => test.id));
        }
    };

    // Check if all tests are selected
    const allTestsSelected = selectedTests.length === recommendedTests.length && recommendedTests.length > 0;

    const handleScheduleClick = () => {
        if (!selectedLab) return;

        if (!selectedDate || !selectedTime || selectedTests.length === 0) {
            toastService.error('Please select a date, time, and at least one test');
            return;
        }

        // Check if there's an existing appointment
        const existing = checkExistingAppointment();
        if (existing) {
            setExistingAppointment(existing);
            setShowConfirmDialog(true);
        } else {
            // No existing appointment, proceed directly
            proceedWithScheduling();
        }
    };

    const proceedWithScheduling = () => {
        if (!selectedLab || !selectedDate || !selectedTime) return;

        const testsToSchedule = recommendedTests.filter(test =>
            selectedTests.includes(test.id)
        );

        if (testsToSchedule.length === 0) return;

        // Create the appointment object
        const newAppointment = {
            id: Date.now().toString(),
            date: selectedDate.toLocaleDateString(),
            time: selectedTime,
            location: `${selectedLab.name}, ${selectedLab.address}, ${selectedLab.city}, ${selectedLab.state}`,
            tests: testsToSchedule.map(t => t.name)
        };

        // Save to localStorage - Replace existing appointment (only store 1)
        try {
            localStorage.setItem('labAppointments', JSON.stringify([newAppointment]));
        } catch (error) {
            console.error('Error saving appointment:', error);
        }

        if (onSchedule) {
            onSchedule({
                lab: selectedLab,
                date: selectedDate,
                time: selectedTime,
                tests: testsToSchedule
            });
        }

        toastService.success('Appointment scheduled successfully.');
        navigate('/appointments');
    };

    const handleConfirmUpdate = () => {
        setShowConfirmDialog(false);
        proceedWithScheduling();
    };

    const handleCancelUpdate = () => {
        setShowConfirmDialog(false);
    };

    const today = new Date();
    const maxDate = new Date();
    maxDate.setMonth(maxDate.getMonth() + 3);

    if (!labId || !selectedLab) {
        return (
            <div className="p-4 max-w-6xl mx-auto">
                <Card className="shadow-md">
                    <div className="p-8 text-center">
                        <i className="pi pi-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                        <h2 className="text-xl font-semibold mb-2">Lab Not Found</h2>
                        <p className="text-gray-600 mb-4">The selected lab could not be found.</p>
                        <Button
                            label="BACK TO LIST"
                            icon="pi pi-arrow-left"
                            onClick={() => navigate('/appointments')}
                        />
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className="lab-schedule-container p-4 max-w-7xl mx-auto">
            {/* Confirm Dialog */}
            <ConfirmDialog
                visible={showConfirmDialog}
                onHide={handleCancelUpdate}
                message={
                    <div className="flex flex-col gap-2">
                        <p className="font-semibold text-lg mb-2">Update Existing Appointment?</p>
                        <p className="mb-3">
                            You already have a scheduled appointment. Proceeding will update your current appointment with the new details.
                        </p>
                        {existingAppointment && (
                            <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                <p className="text-sm font-semibold text-gray-700 mb-1">Current Appointment:</p>
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">Date:</span> {existingAppointment.date}
                                </p>
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">Time:</span> {existingAppointment.time}
                                </p>
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">Location:</span> {existingAppointment.location}
                                </p>
                            </div>
                        )}
                    </div>
                }
                header="Confirm Update"
                icon="pi pi-exclamation-triangle"
                accept={handleConfirmUpdate}
                reject={handleCancelUpdate}
                acceptLabel="Yes, Update"
                rejectLabel="Cancel"
                acceptClassName="p-button-warning"
            />

            <Card className="shadow-md">
                <div className="p-2">
                    {/* Back Button */}
                    <div className="mb-5 text-end">
                        <Button
                            label="BACK TO LIST"
                            icon="pi pi-arrow-left"
                            className="p-button-outlined p-button-sm !py-[0.4rem] !px-4 !text-sm"
                            onClick={() => navigate("/appointments")}
                        />
                    </div>

                    {/* Header with Lab Info */}
                    <div className="flex items-start justify-between mb-6">
                        <h2 className="text-2xl font-semibold text-gray-800">Schedule a New Test</h2>
                        <div className="flex items-start gap-2 text-gray-700">
                            <i className="pi pi-map-marker text-xl text-red-600 mt-1"></i>
                            <div className="text-right">
                                <p className="font-semibold">{selectedLab.address}</p>
                                <p className="text-sm text-gray-600">
                                    {selectedLab.city}, {selectedLab.state} {selectedLab.zipCode}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Main Layout: Calendar on Left, Times + Tests on Right */}
                    <div className="grid grid-cols-1 lg:grid-cols-[410px_1fr] gap-6">
                        {/* Left Column - Calendar Only */}
                        <div>
                            <Calendar
                                value={selectedDate}
                                onChange={(e) => {
                                    setSelectedDate(e.value as Date);
                                    // Clear selected time when date changes (especially when switching to/from Saturday)
                                    setSelectedTime('');
                                }}
                                inline
                                minDate={today}
                                maxDate={maxDate}
                                disabledDays={[0]} // Disable Sundays (0 = Sunday)
                                showWeek={false}
                                className="w-full"
                            />
                        </div>

                        {/* Right Column - Available Times + Recommended Tests */}
                        <div className="space-y-6">
                            {/* Available Times */}
                            <div>
                                <div className="flex items-center justify-between mb-3">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Available Times
                                    </h3>
                                    {isSaturday && (
                                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-md font-medium">
                                            Saturday Hours
                                        </span>
                                    )}
                                </div>
                                <div className="grid grid-cols-4 gap-3">
                                    {timeSlots.map(slot => (
                                        <button
                                            key={slot.id}
                                            onClick={() => setSelectedTime(slot.time)}
                                            disabled={!slot.available}
                                            className={`
                                                py-3 px-4 rounded-lg border-2 font-medium text-sm
                                                transition-all duration-200
                                                ${selectedTime === slot.time
                                                ? 'bg-blue-600 text-white border-blue-600'
                                                : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400 hover:bg-blue-50'
                                            }
                                                ${!slot.available && 'opacity-50 cursor-not-allowed'}
                                            `}
                                        >
                                            {slot.time}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Recommended Tests - Multiple Selection with Checkboxes */}
                            <div>
                                <div className="flex items-center justify-between mb-3">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Recommended Tests
                                    </h3>
                                    <div className="flex items-center gap-2 cursor-pointer" onClick={handleSelectAll}>
                                        <Checkbox
                                            inputId="select-all"
                                            checked={allTestsSelected}
                                            readOnly
                                        />
                                        <label htmlFor="select-all" className="text-sm text-gray-700 cursor-pointer">
                                            Select All
                                        </label>
                                    </div>
                                </div>

                                <div className="border border-gray-300 rounded-lg max-h-[220px] overflow-y-auto custom-scrollbar">
                                    <div className="divide-y divide-gray-200">
                                        {recommendedTests.map(test => (
                                            <div
                                                key={test.id}
                                                className="p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                                                onClick={() => handleTestToggle(test.id)}
                                            >
                                                <div className="flex items-center gap-3">
                                                    <Checkbox
                                                        inputId={`test-${test.id}`}
                                                        name="test"
                                                        value={test.id}
                                                        checked={selectedTests.includes(test.id)}
                                                        readOnly
                                                    />
                                                    <label
                                                        htmlFor={`test-${test.id}`}
                                                        className="flex-1 cursor-pointer text-gray-800"
                                                    >
                                                        {test.name}
                                                    </label>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Schedule Button */}
                            <div className="flex justify-end">
                                <Button
                                    label="Schedule Now"
                                    icon="pi pi-check"
                                    onClick={handleScheduleClick}
                                    className="px-8 py-3"
                                    disabled={!selectedDate || !selectedTime || selectedTests.length === 0}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </Card>

            {/* Custom Scrollbar Styles */}
            <style>{`
                .custom-scrollbar::-webkit-scrollbar {
                    width: 8px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 4px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-thumb {
                    background: #cbd5e1;
                    border-radius: 4px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background: #94a3b8;
                }

                /* PrimeReact Calendar customization */
                .p-calendar .p-datepicker {
                    width: 100%;
                }

                .p-calendar .p-datepicker table {
                    width: 100%;
                }

                /* Enhanced selected date visibility */
                .p-datepicker table td.p-datepicker-today > span {
                    background: #e0f2fe !important;
                    color: #0369a1 !important;
                    font-weight: 600;
                }

                .p-datepicker table td > span.p-highlight {
                    background: #2563eb !important;
                    color: white !important;
                    font-weight: 600;
                    box-shadow: 0 0 0 2px #dbeafe;
                }

                .p-datepicker table td > span.p-highlight:hover {
                    background: #1d4ed8 !important;
                }

                /* Disabled days (Sundays) styling */
                .p-datepicker table td.p-disabled > span {
                    background: #f3f4f6 !important;
                    color: #d1d5db !important;
                    text-decoration: line-through;
                    cursor: not-allowed !important;
                }
            `}</style>
        </div>
    );
};

export default LabSchedule;