import {useEffect, useState, useCallback } from 'react';
import { Card } from 'primereact/card';
import { Dropdown } from 'primereact/dropdown';
import { Divider } from 'primereact/divider';
import { APIProvider, Map, AdvancedMarker, Pin, useMap } from '@vis.gl/react-google-maps';
import { useNavigate } from 'react-router-dom';
// import {environment} from "../../../config/environment.ts";
import {HeaderProps} from "../../../types/layout/Header.ts";

// Types
interface Appointment {
    id: string;
    date: string;
    time: string;
    location: string;
    tests: string[];
}

interface LabLocation {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    distance: number;
    coordinates: {
        lat: number;
        lng: number;
    };
}

interface DistanceOption {
    label: string;
    value: number;
}

// Map Content Component to handle map interactions
const MapContent = ({
                        userLocation,
                        filteredLabs,
                        handleLabClick,
                        selectedDistance
                    }: {
    userLocation: any,
    filteredLabs: LabLocation[],
    handleLabClick: (lab: LabLocation) => void,
    selectedDistance: number
}) => {
    const map = useMap();

    // Update map view when distance or filtered labs change
    useEffect(() => {
        if (!map || filteredLabs.length === 0) return;

        // Create bounds that include user location and all filtered labs
        const bounds = new google.maps.LatLngBounds();

        // Add user location
        bounds.extend(userLocation.coordinates);

        // Add all filtered lab locations
        filteredLabs.forEach(lab => {
            bounds.extend(lab.coordinates);
        });

        // Fit map to show all markers with some padding
        map.fitBounds(bounds, 50);
    }, [map, filteredLabs, userLocation.coordinates, selectedDistance]);

    return (
        <>
            {/* User location marker (home) */}
            <AdvancedMarker
                position={userLocation.coordinates}
                title="Your Location"
            >
                <div className="relative">
                    <Pin
                        background="#2563eb"
                        borderColor="#fff"
                        glyphColor="#fff"
                    />
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                        <i className="pi pi-home text-white text-xs" style={{ marginTop: '-8px' }}></i>
                    </div>
                </div>
            </AdvancedMarker>

            {/* Lab location markers - Only show filtered labs */}
            {filteredLabs.map(lab => (
                <AdvancedMarker
                    key={lab.id}
                    position={lab.coordinates}
                    title={lab.name}
                    onClick={() => handleLabClick(lab)}
                >
                    <Pin
                        background="#dc2626"
                        borderColor="#fff"
                        glyphColor="#fff"
                    />
                </AdvancedMarker>
            ))}
        </>
    );
};

// Main Component
export const LabAppointments = ({setHeader}: HeaderProps) => {
    // ✅ Using React Router for navigation
    const navigate = useNavigate();

    const [appointments, setAppointments] = useState<Appointment[]>([]);
    const [selectedDistance, setSelectedDistance] = useState<number>(5);
    const [userLocation] = useState({
        address: '123 Main St',
        city: 'Columbus',
        state: 'OH',
        zipCode: '43215',
        coordinates: { lat: 39.9612, lng: -82.9988 }
    });

    // Load appointments from localStorage on mount
    useEffect(() => {
        const storedAppointments = localStorage.getItem('labAppointments');
        if (storedAppointments) {
            try {
                const parsed = JSON.parse(storedAppointments);
                setAppointments(parsed);
            } catch (error) {
                console.error('Error loading appointments:', error);
            }
        }
    }, []);

    // Sample lab locations data
    // IMPORTANT: This mock data must match the data in LabSchedule component!
    // When updating, update both components to keep IDs synchronized.
    const [labLocations] = useState<LabLocation[]>([
        {
            id: '1',
            name: 'Quest Diagnostics',
            address: '555 Sycamore St',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43215',
            distance: 1.7,
            coordinates: { lat: 39.9712, lng: -82.9888 }
        },
        {
            id: '2',
            name: 'LabCorp',
            address: '466 Livingston Ave',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43215',
            distance: 3.2,
            coordinates: { lat: 39.9512, lng: -82.9788 }
        },
        {
            id: '3',
            name: 'BioReference Laboratories',
            address: '11 Park Pl',
            city: 'Upper Arlington',
            state: 'OH',
            zipCode: '43221',
            distance: 6.8,
            coordinates: { lat: 40.0012, lng: -83.0588 }
        },
        {
            id: '4',
            name: 'Sonic Healthcare',
            address: '500 Polaris Pkwy, Suite 100',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43082',
            distance: 11.0,
            coordinates: { lat: 40.1512, lng: -82.9988 }
        },
        {
            id: '5',
            name: 'ARUP Laboratories',
            address: '789 Medical Center Dr',
            city: 'Columbus',
            state: 'OH',
            zipCode: '43214',
            distance: 4.5,
            coordinates: { lat: 39.9812, lng: -82.9688 }
        },
        {
            id: '6',
            name: 'Mayo Clinic Laboratories',
            address: '321 Healthcare Blvd',
            city: 'Dublin',
            state: 'OH',
            zipCode: '43017',
            distance: 8.3,
            coordinates: { lat: 40.0912, lng: -83.1188 }
        }
    ]);

    const distanceOptions: DistanceOption[] = [
        { label: 'within 5 miles', value: 5 },
        { label: 'within 10 miles', value: 10 },
        { label: 'within 20 miles', value: 20 }
    ];

    // Filter labs based on selected distance
    const filteredLabs = labLocations.filter(lab => lab.distance <= selectedDistance);

    const handleLabClick = useCallback((lab: LabLocation) => {
        console.log('Selected lab:', lab.name);
        // ✅ Navigate to /appointments/1/schedule, /appointments/2/schedule, etc.
        navigate(`/appointments/${lab.id}/schedule`);
    }, [navigate]);

    useEffect(() => {
        setHeader({
            title: 'Lab Appointments',
            subtitle: 'Schedule and manage laboratory appointments',
            icon: (
                <svg viewBox="0 0 24 24" className="w-4 h-4 stroke-current fill-none stroke-2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
            )
        });

        // Load appointments from localStorage
        const storedAppointments = localStorage.getItem('labAppointments');
        if (storedAppointments) {
            try {
                const parsed = JSON.parse(storedAppointments);
                setAppointments(parsed);
            } catch (error) {
                console.error('Error loading appointments:', error);
                setAppointments([]);
            }
        } else {
            setAppointments([]);
        }
    }, [setHeader]);

    return (
        <div className="lab-appointments-container p-4 max-w-7xl mx-auto">
            {/* Upcoming Appointments Section */}
            <Card className="mb-4 shadow-md">
                <div className="p-4">
                    <h2 className="text-xl font-semibold mb-3 text-gray-800">Upcoming Appointments</h2>
                    {appointments.length === 0 ? (
                        <p className="text-gray-500 italic">No upcoming appointments found.</p>
                    ) : (
                        <div className="space-y-3">
                            {appointments.map(appointment => (
                                <div key={appointment.id} className="p-4 border border-gray-200 rounded-lg">
                                    <div className="flex items-start gap-3">
                                        {/* Calendar Icon */}
                                        <div className="flex-shrink-0">
                                            <i className="pi pi-calendar text-2xl text-gray-700"></i>
                                        </div>

                                        {/* Appointment Details */}
                                        <div className="flex-1">
                                            <p className="font-semibold text-gray-900 mb-1">
                                                {appointment.date} at {appointment.time}
                                            </p>
                                            <p className="text-sm text-gray-700 mb-2">
                                                {appointment.location}
                                            </p>

                                            {/* Tests List */}
                                            {appointment.tests && appointment.tests.length > 0 && (
                                                <div className="space-y-0.5">
                                                    {appointment.tests.map((test, idx) => (
                                                        <p key={idx} className="text-sm text-gray-600 italic">
                                                            {test}
                                                        </p>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </Card>

            <Divider />

            {/* Schedule a New Test Section */}
            <div className="mt-6">
                <h2 className="text-xl font-semibold mb-4 text-gray-800">
                    {appointments.length > 0 ? 'Update Scheduled Test' : 'Schedule a New Test'}
                </h2>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Map Section */}
                    <div className="space-y-4">
                        <Card className="shadow-md overflow-hidden">
                            <div className="h-[300px] w-full">
                                <APIProvider apiKey='AIzaSyC1dcLK9LTz-fsphGYAnDOMZGqmQJ_5cw0'>
                                    <Map
                                        defaultCenter={userLocation.coordinates}
                                        defaultZoom={13}
                                        mapId="lab_appointments_map"
                                        gestureHandling="greedy"
                                        disableDefaultUI={false}
                                        zoomControl={true}
                                        mapTypeControl={false}
                                        streetViewControl={false}
                                        fullscreenControl={false}
                                        clickableIcons={false}
                                    >
                                        <MapContent
                                            userLocation={userLocation}
                                            filteredLabs={filteredLabs}
                                            handleLabClick={handleLabClick}
                                            selectedDistance={selectedDistance}
                                        />
                                    </Map>
                                </APIProvider>
                            </div>
                        </Card>

                        {/* User Location and Distance Filter */}
                        <div className="flex items-center justify-between gap-4 flex-col items-baseline">
                            <div className="flex items-center gap-2 text-gray-700">
                                <i className="pi pi-map-marker text-blue-600"></i>
                                <span className="text-sm">
                                    {userLocation.address}, {userLocation.city}, {userLocation.state} {userLocation.zipCode}
                                </span>
                            </div>

                            <Dropdown
                                value={selectedDistance}
                                options={distanceOptions}
                                onChange={(e) => setSelectedDistance(e.value)}
                                placeholder="Select distance"
                                className="w-48"
                            />
                        </div>
                    </div>

                    {/* Lab Locations List */}
                    <div>
                        <Card className="shadow-md">
                            <div className="max-h-[410px] overflow-y-auto pr-2 custom-scrollbar">
                                <div className="space-y-3">
                                    {filteredLabs.length === 0 ? (
                                        <p className="text-gray-500 text-center py-8">
                                            No labs found within {selectedDistance} miles
                                        </p>
                                    ) : (
                                        filteredLabs.map(lab => (
                                            <div
                                                key={lab.id}
                                                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-all cursor-pointer group"
                                                onClick={() => handleLabClick(lab)}
                                            >
                                                <div className="flex items-start gap-3 flex-1">
                                                    <div className="flex-shrink-0 mt-1">
                                                        <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                                                            <i className="pi pi-map-marker text-white text-xs"></i>
                                                        </div>
                                                    </div>

                                                    <div className="flex-1 min-w-0">
                                                        <p className="font-medium text-gray-900 mb-1">{lab.name}</p>
                                                        <p className="text-sm text-gray-600">
                                                            {lab.address}
                                                        </p>
                                                        <p className="text-sm text-gray-600">
                                                            {lab.city}, {lab.state} {lab.zipCode}
                                                        </p>
                                                    </div>
                                                </div>

                                                <div className="flex items-center gap-3 flex-shrink-0 ml-4">
                                                    <span className="text-sm text-gray-500 italic whitespace-nowrap">
                                                        {lab.distance} miles away
                                                    </span>
                                                    <i className="pi pi-chevron-right text-gray-400 group-hover:text-blue-600 transition-colors"></i>
                                                </div>
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </Card>
                    </div>
                </div>
            </div>

            {/* Custom Scrollbar Styles */}
            <style>{`
                .custom-scrollbar::-webkit-scrollbar {
                    width: 8px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 4px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-thumb {
                    background: #cbd5e1;
                    border-radius: 4px;
                }
                
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background: #94a3b8;
                }
            `}</style>
        </div>
    );
};

export default LabAppointments;