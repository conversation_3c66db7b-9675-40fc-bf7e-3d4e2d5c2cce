import apiClient from "../../auth/api/api";
import {LabLocation} from "../types/LabLocation.ts";
import {UserAppointment} from "../types/UserAppointment.ts";


export const labAppointmentService = {
    getAllLabLocations: async (): Promise<LabLocation[]> => {
        const response = await apiClient.get(`/lab/locations`);
        return response.data;
    },
    getLabLocationById: async (id: string): Promise<LabLocation> => {
        const response = await apiClient.get<LabLocation>(`/lab/locations/${id}`);
        return response.data;
    },
    getUserAppointment: async (): Promise<UserAppointment> => {
        const response = await apiClient.get(`/lab/appointments`);
        return response.data;
    },
    createOrUserAppointment: async (appointment: UserAppointment): Promise<UserAppointment> => {
    const response = await apiClient.post(`/lab/appointments`, appointment);
    return response.data;
},
};