import apiClient from '../api/api.ts';

import { LoginRequest, RegisterRequest, AuthResponse } from '../types/auth.ts';
import {AuthUser} from "../types/authUser.ts";

export const authApi = {
    // Login
    login: async (credentials: LoginRequest): Promise<AuthResponse> => {
        const response = await apiClient.post('/auth/login', credentials);
        return response.data;
    },

    // Register
    register: async (userData: RegisterRequest): Promise<AuthUser> => {
        const response = await apiClient.post('/auth/register', userData);
        return response.data;
    },

    // Refresh token
    refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
        const response = await apiClient.post('/auth/refresh', { refreshToken });
        return response.data;
    },

    // Logout
    logout: async (): Promise<void> => {
        await apiClient.post('/auth/logout');
    },

    // Get current user profile
    getProfile: async (): Promise<AuthUser> => {
        const response = await apiClient.get('/users/profile');
        return response.data;
    },
};