import React, { createContext, useContext, ReactNode, useCallback } from 'react';
import { useCurrentUser } from '../hooks/useAuth';
import { tokenStorage } from '../api/api';
import {useMutation, useQueryClient} from '@tanstack/react-query';
import { authApi } from '../services/auth';
import {LoginRequest} from "../types/auth.ts";
import {useNavigate} from "react-router-dom";
import {toastService} from "../../../services/utils/toastService.ts";
import {AuthUser} from "../types/authUser.ts";


interface AuthContextType {
    user: AuthUser | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    login: (email: string, password: string) => Promise<void>;
    logout: () => void;
    loginError: string | null;
    isLoggingIn: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const { data: user, isLoading } = useCurrentUser();
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    const isAuthenticated = !!user && !!tokenStorage.getAccessToken();

    // Login mutation
    const loginMutation = useMutation({
        mutationFn: (credentials: LoginRequest) => authApi.login(credentials),
        onSuccess: (data) => {
            if (data.requiresTwoFactor) {
                // Handle 2FA flow
                toastService.success(data.message || '2FA code required');
                // Store session token for 2FA
                sessionStorage.setItem('2fa_session', data.sessionToken || '');
                navigate('/verify-2fa'); // Redirect to 2FA verification page
            } else {
                // Store tokens and user
                tokenStorage.setTokens(data.accessToken, data.refreshToken);
                tokenStorage.setUser(data.user);

                // Invalidate and refetch user data
                queryClient.invalidateQueries({ queryKey: ['user'] });

                toastService.success('Login successful!');
                navigate('/dashboard'); // Redirect to dashboard
            }
        },
        onError: () => {
            toastService.error("Invalid email or password.");
        },
    });

    // Logout mutation
    const logoutMutation = useMutation({
        mutationFn: authApi.logout,
        onSuccess: () => {
            tokenStorage.clearTokens();
            queryClient.clear(); // Clear all cached data
            toastService.success('Logged out successfully');
            navigate('/login');
        },
        onError: () => {
            // Even if API call fails, clear local storage
            tokenStorage.clearTokens();
            queryClient.clear();
            navigate('/login');
        },
    });

    // Login function implementation
    const login = useCallback(async (email: string, password: string): Promise<void> => {
        try {
            await loginMutation.mutateAsync({ email, password });
        } catch (error) {
            // Error is already handled by the mutation's onError
            throw error; // Re-throw so the calling component can handle it if needed
        }
    }, [loginMutation]);

    // Logout function implementation
    const logout = useCallback(() => {
        logoutMutation.mutate();
    }, [logoutMutation]);

    return (
        <AuthContext.Provider
            value={{
                user: user || null,
                isLoading,
                isAuthenticated,
                login,
                logout,
                loginError: loginMutation.error?.message || null,
                isLoggingIn: loginMutation.isPending,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within AuthProvider');
    }
    return context;
};