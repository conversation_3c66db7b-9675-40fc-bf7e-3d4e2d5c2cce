import {AuthUser} from "./authUser.ts";


export interface LoginRequest {
    email: string;
    password: string;
}

export interface RegisterRequest {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
}

export interface AuthResponse {
    accessToken: string;
    refreshToken: string;
    tokenType: string;
    expiresIn: number;
    user: AuthUser;
    requiresTwoFactor?: boolean;
    sessionToken?: string;
    message?: string;
}