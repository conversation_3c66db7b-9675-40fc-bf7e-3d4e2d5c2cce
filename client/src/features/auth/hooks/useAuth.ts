import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { authApi } from '../services/auth';
import { tokenStorage } from '../api/api';
import { toastService } from "../../../services/utils/toastService.ts";
import axios from "axios";

export const useLogin = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    return useMutation({
        mutationFn: authApi.login,
        onSuccess: (data) => {
            if (data.requiresTwoFactor) {
                // Handle 2FA flow
                toastService.success("2FA code required");
                // Store session token for 2FA
                sessionStorage.setItem('2fa_session', data.sessionToken || '');
                navigate('/verify-2fa');
            } else {
                // Store tokens and user
                tokenStorage.setTokens(data.accessToken, data.refreshToken);
                tokenStorage.setUser(data.user);

                // Invalidate and refetch user data
                queryClient.invalidateQueries({ queryKey: ['user'] });

                toastService.success('Login successful!');
                navigate('/dashboard');
            }
        },
        onError: (error: any) => {
            const message = error.response?.data?.message || 'Login failed';
            toastService.error(message);
        },
    });
};

export const useRegister = () => {
    return useMutation({
        mutationFn: authApi.register,
        onSuccess: () => {
            toastService.success('Registration successful!');
            // navigate('/auth', {
            //     state: { email: variables.email }
            // });
        },
        onError: (error: any) => {
            console.error('Error creating user:', error);

            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                switch (status) {
                    case 400:
                        toastService.error('Please check your form data and try again.');
                        break;
                    case 409:
                        toastService.error('An account with this email already exists.');
                        break;
                    default:
                        toastService.error('Something went wrong. Please try again later.');
                }
            } else {
                toastService.error('❌ Network error. Please check your connection.');
            }
        },
    });
};

export const useLogout = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    return useMutation({
        mutationFn: authApi.logout,
        onSuccess: () => {
            tokenStorage.clearTokens();
            queryClient.clear(); // Clear all cached data
            toastService.success('Logged out successfully');
            navigate('/auth', { replace: true });
        },
        onError: () => {
            // Even if API call fails, clear local storage
            tokenStorage.clearTokens();
            queryClient.clear();
            navigate('/auth', { replace: true });
            toastService.error('Logout failed, but you have been signed out locally');
        },
    });
};

export const useCurrentUser = () => {
    const token = tokenStorage.getAccessToken();

    const query = useQuery({
        queryKey: ['user'],
        queryFn: () => authApi.getProfile(), // Add arrow function wrapper
        enabled: !!token,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        // Remove placeholderData if causing issues
    });

    return query;
};