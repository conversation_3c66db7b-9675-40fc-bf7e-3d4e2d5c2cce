import {useEffect, useState, useRef} from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card } from 'primereact/card';
import { Avatar } from 'primereact/avatar';
import { Divider } from 'primereact/divider';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import { RadioButton } from 'primereact/radiobutton';
import { confirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { userService } from "../../services/userService.ts";
import { HeaderProps } from "../../../../types/layout/Header.ts";
import {EditControls} from "../../../../components/common/EditControls.tsx";
import {useUnsavedChangesStore} from "../../../../stores/useUnsavedChangesStore.ts";


const UserProfile = ({setHeader}: HeaderProps) => {
    const [isEditing, setIsEditing] = useState(false);
    const [preferredComm, setPreferredComm] = useState('email');
    const [originalPreferredComm, setOriginalPreferredComm] = useState('email');
    const toast = useRef<Toast>(null);

    const setHasUnsavedChanges = useUnsavedChangesStore(state => state.setHasUnsavedChanges);

    useEffect(() => {
        setHeader({
            title: 'User Profile',
            subtitle: 'Personal details and account information'
        });
    }, [setHeader]);

    const { data, error, isLoading, refetch } = useQuery<any>({
        queryKey: ["userProfile"],
        queryFn: userService.getUserProfile
    });

    // Mutation for updating user profile
    const updateProfileMutation = useMutation({
        mutationFn: (updateData: { preferredCommMethod: string }) =>
            userService.updateUserProfile(updateData),
        onSuccess: () => {
            setOriginalPreferredComm(preferredComm);
            setIsEditing(false);
            setHasUnsavedChanges(false);

            toast.current?.show({
                severity: 'success',
                summary: 'Success',
                detail: 'Preferences saved successfully',
                life: 3000
            });

            refetch();
        },
        onError: (error: any) => {
            toast.current?.show({
                severity: 'error',
                summary: 'Error',
                detail: error?.response?.data?.message || 'Failed to save preferences',
                life: 3000
            });
        }
    });

    // Set initial value from data when it loads
    useEffect(() => {
        if (data?.preferredCommMethod) {
            setPreferredComm(data.preferredCommMethod);
            setOriginalPreferredComm(data.preferredCommMethod);
        }
    }, [data]);

    // Track unsaved changes and update store
    useEffect(() => {
        const hasChanges = preferredComm !== originalPreferredComm;
        setHasUnsavedChanges(hasChanges);
    }, [preferredComm, originalPreferredComm, setHasUnsavedChanges]);

    // Handle browser navigation away with unsaved changes
    useEffect(() => {
        const handleBeforeUnload = (e: BeforeUnloadEvent) => {
            if (preferredComm !== originalPreferredComm) {
                e.preventDefault();
                e.returnValue = '';
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }, [preferredComm, originalPreferredComm]);

    // Cleanup: reset unsaved changes when component unmounts
    useEffect(() => {
        return () => {
            setHasUnsavedChanges(false);
        };
    }, [setHasUnsavedChanges]);

    const handleEdit = () => {
        setIsEditing(true);
    };

    const handleSave = () => {
        updateProfileMutation.mutate({
            preferredCommMethod: preferredComm
        });
    };

    const handleCancel = () => {
        if (preferredComm !== originalPreferredComm) {
            confirmDialog({
                message: 'You have unsaved changes. Are you sure you want to cancel?',
                header: 'Unsaved Changes',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    setPreferredComm(originalPreferredComm);
                    setIsEditing(false);
                    setHasUnsavedChanges(false);
                },
                reject: () => {
                    // User clicked "No", stay in edit mode
                }
            });
        } else {
            setIsEditing(false);
        }
    };

    if (isLoading) {
        return (
            <div className="bg-gray-50 min-h-screen flex items-center justify-center">
                <ProgressSpinner />
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-gray-50 min-h-screen p-6">
                <div className="max-w-6xl mx-auto">
                    <Message
                        severity="error"
                        text={error instanceof Error ? error.message : 'Failed to load user profile'}
                        className="w-full"
                    />
                    <Button label="Retry" icon="pi pi-refresh" onClick={() => refetch()} className="mt-4" />
                </div>
            </div>
        );
    }

    if (!data) {
        return (
            <div className="bg-gray-50 min-h-screen flex items-center justify-center">
                <Message severity="warn" text="No user data available" />
            </div>
        );
    }

    return (
        <div className="bg-gray-50 min-h-screen p-6">
            <Toast ref={toast} />
            <div className="max-w-5xl mx-auto">
                {/* Header Card */}
                <Card className="shadow-sm mb-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                            <Avatar
                                label={data.firstName.charAt(0) + data.lastName.charAt(0)}
                                size="xlarge"
                                shape="square"
                                className="!w-24 !h-24 text-5xl"
                                style={{ backgroundColor: '#3b82f6', color: '#ffffff' }}
                            />
                            <div>
                                <h1 className="text-3xl font-bold text-gray-800 mb-1">
                                    {data.firstName} {data.lastName}
                                </h1>
                                <p className="text-blue-600 font-medium text-lg mb-2">{data.title}</p>
                                <div className="flex gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <i className="pi pi-envelope"></i>
                      {data.email}
                  </span>
                                    <span className="flex items-center gap-1">
                    <i className="pi pi-phone"></i>
                                        {data.phone}
                  </span>
                                </div>
                            </div>
                        </div>
                        <EditControls
                            isEditing={isEditing}
                            onEdit={handleEdit}
                            onSave={handleSave}
                            onCancel={handleCancel}
                            isSaving={updateProfileMutation.isPending}
                        />
                    </div>
                </Card>

                {/* Details Sections */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <Card title="Personal Details" className="shadow-sm">
                        <div className="space-y-4">
                            <div className="flex justify-between py-2 border-b border-gray-100">
                                <span className="text-gray-600">Full Name</span>
                                <span className="font-medium text-gray-800">
                  {data.firstName} {data.middleName} {data.lastName}
                </span>
                            </div>
                            <div className="flex justify-between py-2 border-b border-gray-100">
                                <span className="text-gray-600">Date of Birth</span>
                                <span className="font-medium text-gray-800">
                  {new Date(data.birthDate).toLocaleDateString()}
                </span>
                            </div>
                            <div className="flex justify-between py-2 border-b border-gray-100">
                                <span className="text-gray-600">Gender</span>
                                <span className="font-medium text-gray-800">
                  {data.gender === 'F' ? 'Female' : data.gender === 'M' ? 'Male' : 'Other'}
                </span>
                            </div>
                        </div>
                    </Card>

                    <Card title="Address" className="shadow-sm">
                        <div className="space-y-2">
                            <p className="text-gray-800 font-medium">{data.address1}</p>
                            {data.address2 && <p className="text-gray-800">{data.address2}</p>}
                            <p className="text-gray-800">{data.city}, {data.state}</p>
                            <Divider />
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p className="text-gray-500">Member Since</p>
                                    <p className="font-medium text-gray-800">
                                        {new Date(data.createdAt).toLocaleDateString()}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-gray-500">Status</p>
                                    <p className="font-medium text-gray-800">
                                        {data.active ? 'Active' : 'Inactive'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Preferences */}
                <Card title="Preferences" className="shadow-sm">
                    <div className="space-y-4">
                        <div>
                            <h3 className="text-gray-700 font-semibold mb-3">Method of Communication</h3>
                            <div className="flex flex-col gap-3">
                                <div className="flex items-center gap-3">
                                    <RadioButton
                                        inputId="comm_sms"
                                        name="communication"
                                        value="SMS"
                                        onChange={(e) => setPreferredComm(e.value)}
                                        checked={preferredComm === 'SMS'}
                                        disabled={!isEditing}
                                    />
                                    <label
                                        htmlFor="comm_text"
                                        className={`${isEditing ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'} text-gray-800`}
                                    >
                                        <i className="pi pi-mobile mr-2 text-blue-600"></i>
                                        Text to mobile
                                    </label>
                                </div>
                                <div className="flex items-center gap-3">
                                    <RadioButton
                                        inputId="comm_email"
                                        name="communication"
                                        value="EMAIL"
                                        onChange={(e) => setPreferredComm(e.value)}
                                        checked={preferredComm === 'EMAIL'}
                                        disabled={!isEditing}
                                    />
                                    <label
                                        htmlFor="comm_email"
                                        className={`${isEditing ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'} text-gray-800`}
                                    >
                                        <i className="pi pi-envelope mr-2 text-blue-600"></i>
                                        Email
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default UserProfile;