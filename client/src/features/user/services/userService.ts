import apiClient from "../../auth/api/api";

export const userService = {
    getUserProfile: async (): Promise<any[]> => {
        const response = await apiClient.get('/users/profile');
        return response.data;
    },

    updateUserProfile: async (data: { preferredCommMethod: string }): Promise<any> => {
        const response = await apiClient.put('/users/profile', data);
        return response.data;
    }
};