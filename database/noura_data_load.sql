\connect demo

SET role noura;

INSERT INTO roles (role_id, role_name)
  VALUES (0, 'Admin'), (1,'Patient');

INSERT INTO users (user_id, login_id, is_active, password,
  first_name, middle_name, last_name, title,
  birth_date, gender, email, phone, address_1,
  address_2, city, state, role_id, created_date,
  last_login_date, user_version)

    VALUES

    (1, 'admin', TRUE, 'MollyAnd<PERSON>ich<PERSON>', 'Admin', <PERSON>U<PERSON>, 'Administrator',
    'Mr.', '02-22-1932', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 0, '02-22-2025',
    '02-22-2025', 0),
    (2, 'sam', TRUE, 'Molly<PERSON>nd<PERSON>ich<PERSON>', '<PERSON>', NULL, 'W<PERSON>',
     'Mr.', '05-10-1940', 'M', '<EMAIL>', '************',
     '1600 Pennsylvania Ave.', <PERSON>ULL, 'Washington', 'DC', 0, '02-22-2025',
     '02-22-2025', 0),
    (3,'georgew01', TR<PERSON>, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>', <PERSON><PERSON><PERSON>, 'Washington',
    'Mr.', '02-22-1932', '<PERSON>', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (4,'johna02', TRUE, '<PERSON><PERSON>ndMichael', '<PERSON>', NULL, '<PERSON>',
    'Mr.', '10-30-1935', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (5,'thomasj03', TRUE, 'MollyAndMichael', 'Thomas', NULL, 'Jefferson',
    'Mr.', '04-13-1926', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (6,'jamesm04', TRUE, 'MollyAndMichael', 'James', NULL, 'Madison',
    'Mr.', '03-16-1951', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (7,'jamesm05', TRUE, 'MollyAndMichael', 'James', NULL, 'Monroe',
    'Mr.', '04-28-1958', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (8,'johnqa06', TRUE, 'MollyAndMichael', 'John', 'Quincy', 'Adams',
    'Mr.', '06-11-1967', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (9,'andrewj07', TRUE, 'MollyAndMichael', 'Andrew', NULL, 'Jackson',
    'Mr.', '03-15-1967', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (10,'martinvb08', TRUE, 'MollyAndMichael', 'Martin', NULL, 'Van Buren',
    'Mr.', '12-05-1962', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (11,'williamhh09', TRUE, 'MollyAndMichael', 'William', 'Henry', 'Harrison',
    'Mr.', '02-09-1941', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (12,'johnt10', TRUE, 'MollyAndMichael', 'John', NULL, 'Tyler',
    'Mr.', '03-29-1990', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (13,'jameskp11', TRUE, 'MollyAndMichael', 'James', 'Knox', 'Polk',
    'Mr.', '11-02-1995', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (14,'zacharyt12', TRUE, 'MollyAndMichael', 'Zachary', NULL, 'Taylor',
    'Mr.', '11-24-1984', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (15,'millardf13', TRUE, 'MollyAndMichael', 'Millard', NULL, 'Fillmore',
    'Mr.', '01-07-2000', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (16,'franklinp14', TRUE, 'MollyAndMichael', 'Franklin', NULL, 'Pierce',
    'Mr.', '11-23-2002', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (17,'jamesb15', TRUE, 'MollyAndMichael', 'James', NULL, 'Buchanan',
    'Mr.', '04-23-1991', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (18,'abrahaml16', TRUE, 'MollyAndMichael', 'Abraham', NULL, 'Lincoln',
    'Mr.', '02-12-1999', 'M', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (19,'marthadc01', TRUE, 'MollyAndMichael', 'Martha', 'Dandridge', 'Custis',
    'Mrs.', '06-02-1931', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (20,'abigails02', TRUE, 'MollyAndMichael', 'Abigail', NULL, 'Smith',
    'Mrs.', '11-22-1944', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (21,'marthasw03', TRUE, 'MollyAndMichael', 'Martha', 'Skelton', 'Wales',
    'Mrs.', '10-30-1948', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (22,'dolleytp04', TRUE, 'MollyAndMichael', 'Dolley', 'Todd', 'Payne',
    'Mrs.', '05-20-1968', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (23,'elizabethk05', TRUE, 'MollyAndMichael', 'Elizabeth', NULL, 'Kortright',
    'Mrs.', '06-30-1968', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (24,'louisacj06', TRUE, 'MollyAndMichael', 'Louisa', 'Catherine', 'Johnson',
    'Mrs.', '02-12-1975', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (25,'racheld07', TRUE, 'MollyAndMichael', 'Rachel', NULL, 'Donelson',
    'Mrs.', '06-15-1967', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (26,'hannahh08', TRUE, 'MollyAndMichael', 'Hannah', NULL, 'Hoes',
    'Mrs.', '03-08-1983', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (27,'annats09', TRUE, 'MollyAndMichael', 'Anna', 'Tuthill', 'Symmes',
    'Mrs.', '07-25-1975', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (28,'letitiac10', TRUE, 'MollyAndMichael', 'Letitia', NULL, 'Christian',
    'Mrs.', '11-12-1990', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (29,'sarahc11', TRUE, 'MollyAndMichael', 'Sarah', NULL, 'Childress',
    'Mrs.', '09-04-2003', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (30,'margarets12', TRUE, 'MollyAndMichael', 'Margaret', 'Mackall', 'Smith',
    'Mrs.', '09-21-1988', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (31,'abigailp13', TRUE, 'MollyAndMichael', 'Abigail', NULL, 'Powers',
    'Mrs.', '03-13-1998', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (32,'janea14', TRUE, 'MollyAndMichael', 'Jane', 'Means', 'Appleton',
    'Mrs.', '03-12-2006', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (33,'harrietj15', TRUE, 'MollyAndMichael', 'Harriet', 'Rebecca Lane', 'Johnston',
    'Mrs.', '05-09-1930', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0),
    (34,'maryt16', TRUE, 'MollyAndMichael', 'Mary', 'Ann', 'Todd',
    'Mrs.', '12-13-1999', 'F', '<EMAIL>', '************',
    '1600 Pennsylvania Ave.', NULL, 'Washington', 'DC', 1, '02-22-2025',
    '02-22-2025', 0);

UPDATE users SET password = crypt('MollyAndMichael', gen_salt('bf', 10));

INSERT INTO test_groups (test_group_id, test_group_name, test_group_description)

VALUES

    (0, 'Basic Metabolic Panel (BMP)', 'A BMP is a group of 8 blood tests that measure electrolytes, kidney function, and glucose. It''s used to check general health, monitor chronic conditions (like kidney disease, diabetes, hypertension), and guide treatment decisions (like IV fluids or medications).'),
    (1, 'Comprehensive Metabolic Panel (CMP)', 'A CMP is a blood test that measures 14 different substances to provide a snapshot of your metabolism, kidney function, liver health, electrolytes, blood sugar, and protein balance. It is typically used for routine health screening and chronic condition monitoring.'),
    (2, 'Electrolyte Panel', 'An electrolyte panel typically measures the four core electrolytes and related values that help maintain your body''s fluid balance, acid-base balance, and proper muscle/nerve function.'),
    (3, 'Lipid Panel', 'A lipid panel measures the levels of different fats in your plood. It is primarily used to assess your risk for cardiovascular disease - including heart attack, stroke, and atherosclerosis.'),
    (4, 'Liver Panel', 'A liver panel is a group of blood tests that give insight into how well your liuver is working, whether it''s inflamed or injured, and whether bile flow is normal.'),
    (5, 'Renal Panel', 'A renal panel is a group of blood tests used to evaluate kidney function, electrolyte balance, and related metabolic health.'),
    (6, 'Calcium', 'Measures calcium, a key mineral in the body that gives valuable information about bone health, kidney function, parathyroid function, and more.'),
    (7, 'Magnesium', 'Measurement of the magnesium in the blood to evaluate electrolyte balance, muscle and nerve function, and certain metabolic conditions.'),
    (8, 'Vitamin D', 'Measurement of the level of vitamin D, an essential vitamin that supports bone health and provides anti-inflammatory and other properties that play a role in maintaining normal muscle, immune, and nervous system functions.'),
    (9, 'Parathyroid Hormone (PTH)', 'Measurement of the level of parathyroid horome in the blood that is produced by the parathyroid glands and plays a critical role  in regulating blood calcium levels, phosphate levels, and vitamin D activation.'),
    (10, 'Complete Blood Count (CBC) with Differential', 'One of the most common and comprehensive blood tests used in medicine, the CBC gives a detailed picture of a person''s overall blood health, include red blood cells, white blood cells, and platelets.'),
    (11, 'Thyroid Panel', 'Measures one or more hormones related to thyroid function, critical for regulating metabolism, energy, heart rate, and growth.'),
    (12, 'Thyroid Antibodies', 'Measures one or more autoantibodies related to thyroid function, critical for regulating metabolism, energy, heart rate, and growth.'),
    (13, 'DHEA Sulfate', 'A common endocrine test that helps evaluate the adrenal gland function and androgen hormone levels in both men and women.'),
    (14, 'C-Reactive Protein', 'A common blood test that measures inflammation in the body. It is widely used in routine medicine for infection monitoring and chronic disease assessment.'),
    (15, 'Erythrocyte Sedimentation Rate', 'A common blood test used as a non-specific marker of inflammation, especially for chronic or systemic conditions. The test measures how quickly red blood cells settle to the bottom of a test tube in one hour.'),
    (16, 'Sex Hormone Binding Globulin', 'A key hormone test that helps assess the amount of sex hormones available to act on tissues. It is commonly used when investigating hormone imbalances in men and women.'),
    (17, 'Testosterone', 'Testosterone is one of the most common hormone tests used to evaluate reproductive, sexual, and endocrine health in both men and women. In men, testosterone is responsible for male sexual development, muscle mass, bone density, red blood cell production, and libido. While in women, testosterone is produced in smaller amounts and contributes to libido, bone health, and overall energy.'),
    (18, 'Homocysteine', 'A homocysteine blood test is an important marker for cardiovascular health, B-vitamin status, and certain metabolic disorders. It''s commonly used in both general medicine and neurology. Homocysteine is an amino acid that is produced as the body metabolizes methionine and is normally broken down with the help of folate, vitamin B12, and vitamin B6.'),
    (19, 'Follicle Stimulating Hormone', 'One of the key reproductive hormon tests used to assess fertilitiy, menstrual health, and pituitary gland function. In women, FSH stimulates ovarian follicles to grow and helps to regulate the menstrual cycle. In men, FSH stimulates sperm production.'),
    (20, 'Estradiol', 'One of the most important hormone tests for evaluating reproductive health, menstrual cycles, and estrogen-related conditions. Estradiol is the most potent form of estrogen produced mainly in the ovaries in women, but also in smaller amounts by the adrenal glands and the testes in men.'),
    (21, 'Luteinizing Hormone', 'One of the cornerstone reproductive hormone tests, often ordered alongside follicle stimulating hormone and estradiol. It gives valuable insight into fertility, puberty, and pituitary function. In women, LH triggers ovulation and supports progesterone production. In men, LH stimulates the Leydig cells in the testes to produce testosterone.'),
    (22, 'Progesterone', 'A key reproductive hormone test that helps evaluate ovulation, fertility, menstrual health, and certain pregnancy-related conditions. Progesterone''s primary role is to prepare and maintain the uterine lining for implantation and support early pregnancy.'),
    (23, 'Celiac Panel', 'A celiac panel is a set of blood tests that help diagnose celiac disease, an autoimmune condition triggered by gluten. It screens for specific antibodies that the immune system produces in response to gluten ingestion and intestinal injury.'),
    (24, 'Hemoglobin A1c', 'One of the most widely used tests for diagnosing and monitoring diabetes, HgA1c tests provides a long-term view of blood sugar control by measuring the percentage of hemoglobin in red blood cells that has glucose attached. It is used to diagnose diabetes and monitor treatment effectiveness.'),
    (25, 'Lipoprotein (a)', 'A lipoprotein (a) test is a specialized cholesterol-related test that measures a genetically determined form of LDL cholesterol and is an independent risk factor for heart disease. Elevated Lp(a) is largely genetic and is considered a risk-enhancing factor for atherosclerotic cardiovascular disease.'),
    (26, 'NMR Protein Fractionation', 'NMR Protein Fractionation is a specialized blood test that goes beyond a standard lipid panel to measure the size and concentration of lipoprotein particles using Nuclear Magnetic Resonance (NMR) spectroscopy. It is often ordered to provide a more detailed cardiovascular risk profile, especially for patients with metabolic syndrom, diabetes, or unexplained high cardiovascular risk despite normal standard lipids.'),
    (27, 'Iodine', 'An iodine blood test is a specialized test that measures how much iodine is circulating in the blood. Iodine is an essential trace element that the thyroid uses to make thyroid hormones.'),
    (28, 'Vitamin B6', 'A vitamin B6 blood test is used to measure the levels of pyridoxine (B6) and its active forms in the blood in order to check for deficiency, excess, or issues with metabolism. Vitamin B6 is a water-soluble vitamin essential for amino acid metabolism, neurotransmitter synthesis, hemoglobin production, and glucose metabolism.'),
    (29, 'Vitamin B2', 'A vitamin B2 blood test measures the levels of riboflavin (B2), an essential water-soluble vitamin that plays a key role in energy production and cellular metabolism.'),
    (30, 'Vitamin B12', 'One of the most commonly ordered nutritional and hematologic tests, a vitamin B12 test evaluates red blood cell production, neurological health, and DNA synthesis.'),
    (31, 'Folic Acid', 'A folic acid test is a key nutritional and hematologic test used to evaluate folate status, which is essential for red blood cell production, DNA synthesis, and methylation pathways.'),
    (32, 'Vitamin B12 Binding Capacity', 'A B12 binding capacity test is a more specialized test to evaluate how well the body can bind and transport vitamin B12, which is important for proper absorption and utilization.'),
    (33, 'Rheumatoid Factor (RF)', 'One of the key tests used in the evaluation of autoimmune arthritis, especially rheumatoid arthritis. Rheumatoid factor is an autoantibody commonly found in autoimmune disease, chronic infections, and occassionally in health individuals.'),
    (34, 'Anti-nuclear Antibody (ANA)', 'An ANA blood test is one of the most important screening tests in autoimmune and rheumatologic medicine. It helps identify immune system activity actively directed against the body''s own cell nuclei.'),
    (35, 'Extractable Nuclear Antigen Antibodies (ENA) Panel', 'An important follow up test to a positive ANA result, the ENA panel helps pinpoint which specific nuclear antigens the immune system is targeting, allowing doctors to narrow down which autoimmune condition is most likely.'),
    (36, 'Complement Levels', 'The complement levels blood test is an important immune system test, especially for autoimmune disease evaluation. It measures proteins that are part of the complement system, which helps fight infection and mediate inflammation.'),
    (37, 'Iron Panel', 'The iron studies panel is one of the most common tests used to elevate iron status, anemia, and disorders of iron metabolism. It includes several related measurements that, when interpreted together, give a detailed picture of iron balance.');

INSERT INTO test_definitions (test_id, test_group_id, test_name, test_name_abbreviation, test_description, test_unit)

VALUES

    (0, 0, 'Glucose', '', 'Also known as blood sugar, this serves as energy for the body and brain.', 'mg/dL'),
    (1, 0, 'Calcium', '', 'A mineral essential to the cardiovascular and nervous systems.', 'mg/dL'),
    (2, 0, 'Sodium', '', 'The main electrolyte regulating fluid balance.', 'mmol/L'),
    (3, 0, 'Potassium', '', 'Key electrolyte for heart and muscle function.', 'mmol/L'),
    (4, 0, 'Bicarbonate (CO2)', 'CO2', 'Buffering capacity of blood (indicator of acid-base balance).', 'mmol/L'),
    (5, 0, 'Chloride', '', 'Electrolyte that helps maintain acid-base balance.', 'mmol/L'),
    (6, 0, 'Blood Urea Nitrogen (BUN)', 'BUN', 'Waste product from protein metabolism.', 'mg/dL'),
    (7, 0, 'Creatinine', '', 'Byproduct of muscle metabolism that is filtered by the kidneys.', 'mg/dL'),
    (8, 1, 'Alanine Aminotransferase (ALT)', 'ALT', 'Liver enzyme', 'IU/L'),
    (9, 1, 'Alkaline Phosphatase (ALP)', 'ALP', 'Enzyme from liver, bile ducts, and bone.', 'IU/L'),
    (10, 1, 'Aspartate Aminotransferase (AST)', 'AST', 'Liver and muscle enzyme.', 'IU/L'),
    (11, 1, 'Bicarbonate (CO2)', 'CO2', 'Buffering capacity of blood (indicator of acid-base balance).', 'mmol/L'),
    (12, 1, 'Total Bilirubin', '', 'Breakdown product of red blood cells.', 'mg/dL'),
    (13, 1, 'Blood Urea Nitrogen (BUN)', 'BUN', 'Waste product from protein metabolism.', 'mg/dL'),
    (14, 1, 'Calcium', '', 'A mineral essential to the cardiovascular and nervous systems.', 'mg/dL'),
    (15, 1, 'Chloride', '', 'Electrolyte that helps maintain acid-base balance.', 'mmol/L'),
    (16, 1, 'Creatinine', '', 'Byproduct of muscle metabolism that is filtered by the kidneys.', 'mg/dL'),
    (17, 1, 'Glucose', '', 'Also known as blood sugar, this serves as energy for the body and brain.', 'mg/dL'),
    (18, 1, 'Potassium', '', 'Key electrolyte for heart and muscle function.', 'mmol/L'),
    (19, 1, 'Sodium', '', 'The main electrolyte regulating fluid balance.', 'mmol/L'),
    (20, 1, 'Total Protein', '', 'Total amount of albumin and globulins.', 'g/dL'),
    (21, 1, 'Albumin', '', 'Main blood protein made by the liver.', 'g/dL'),
    (22, 1, 'Globulins', '', 'Non-albumin proteins including immune globulins and transport proteins.', 'g/dL'),
    (23, 1, 'Albumin/Globulin (A/G) Ratio', 'A/G Ratio', 'Balance between albumin and globulins.', 'Ratio'),
    (24, 2, 'Sodium', '', 'The main electrolyte regulating fluid balance.', 'mmol/L'),
    (25, 2, 'Potassium', '', 'Key electrolyte for heart and muscle function.', 'mmol/L'),
    (26, 2, 'Chloride', '', 'Electrolyte that helps maintain acid-base balance.', 'mmol/L'),
    (27, 2, 'Bicarbonate (CO2)', 'CO2', 'Buffering capacity of blood (indicator of acid-base balance).', 'mmol/L'),
    (28, 2, 'Anion Gap', '', 'A calculated value used to evaluate metabolic acidosis causes.', 'mEq/L'),
    (29, 3, 'Total Cholesterol', '', 'Overall cholesterol level, inclusive of all types.', 'mg/dL'),
    (30, 3, 'Low-density Lipoprotein (LDL) Cholesterol', 'LDL', 'Bad cholesterol, carried by low density fats.', 'mg/dL'),
    (31, 3, 'High-density Lipoprotein (HDL) Cholesterol', 'HDL', 'Good cholesterol, carried by high density fats.', 'mg/dL'),
    (32, 3, 'Triglycerides', '', 'Fatty acids stored in blood and used for energy.', 'mg/dL'),
    (33, 3, 'Very Low-Density Lipoprotein (VLDL)', 'VLDL', 'Estimated amount of triglyceride-carrying particles.', 'mg/dL'),
    (34, 3, 'Cholesterol/HDL Ratio', '', 'The proportion of total cholesterol made up of good cholesterol used as a risk indicator for cardiovascular disease.', 'Ratio'),
    (35, 4, 'Aspartate Aminotransferase (AST)', 'AST', 'An enzyme in liver, muscle, and heart cells.', 'IU/L'),
    (36, 4, 'Alanine Aminotransferase (ALT)', 'ALT', 'An enzyme mainly in liver cells.', 'IU/L'),
    (37, 4, 'Alkaline Phosphatase (ALP)', 'ALP', 'Enzyme in bile ducts, bone, and liver cells.', 'IU/L'),
    (38, 4, 'Total Bilirubin', '', 'Breakdown product of red blood cells.', 'mg/dL'),
    (39, 4, 'Direct (Conjugated) Bilirubin', '', 'Bilirubin already processed by the liver.', 'mg/dL'),
    (40, 4, 'Indirect (Unconjugated) Bilirubin', '', 'Calculated value of bilirubin waiting to be processed by the liver (Total - Direct)', 'mg/dL'),
    (41, 4, 'Albumin', '', 'Main blood protein made by the liver.', 'g/dL'),
    (42, 4, 'Gamma-glutamyl Transferase (GGT)', 'GGT', 'An enzyme found mainly in the liver and bile ducts.', 'IU/L'),
    (43, 4, '5'' nucleotidase (5''-NT)', '5''-NT', 'An enzyme that catalyzes the removal of phosphate groups from nucleotides.', 'U/L'),
    (44, 4, 'Total Protein', '', 'Measurement of albumin and globulins.', 'g/dL'),
    (45, 4, 'Globulins', '', 'A group of blood proteins other than albumin, calculated by subtracting albumin from total protein.', 'g/dL'),
    (46, 4, 'Prothrombin Time (PT)', 'PT', 'Time for blood to clot.', 'Seconds'),
    (47, 4, 'Lactate Dehydrogenase (LDH)', 'LDH', 'An enzyme found in almost all tissues used during energy production.', 'IU/L'),
    (48, 5, 'Glucose', '', 'Also known as blood sugar, this serves as energy for the body and brain.', 'mg/dL'),
    (49, 5, 'Phosphorous', '', 'A mineral filtered by kidneys that is important for bone health.', 'mg/dL'),
    (50, 5, 'Calcium', '', 'A mineral used for bone, nerve, and muscle function', 'mg/dL'),
    (51, 5, 'Potassium', '', 'Critical intracellular electrolyte.', 'mmol/L'),
    (52, 5, 'Sodium', '', 'Major intracellular electrolyte.', 'mmol/L'),
    (53, 5, 'Chloride', '', 'Major extracellular anion.', 'mmol/L'),
    (54, 5, 'Bicarbonate (CO2)', 'CO2', 'Buffering capacity of blood (indicator of acid-base balance).', 'mmol/L'),
    (55, 5, 'Albumin', '', 'Main blood protein made by the liver.', 'g/dL'),
    (56, 5, 'Creatinine', '', 'Waste product of muscle metabolism.', 'mg/dL'),
    (57, 5, 'Blood Urea Nitrogen (BUN)', 'BUN', 'Waste product of protein metabolism.', 'mg/dL'),
    (58, 5, 'Anion Gap', '', 'A calculated value used to evaluate metabolic acidosis causes.', 'mEq/L'),
    (59, 5, 'Estimated Glomerular Filtration Rate (eGFR)', 'eGFR', 'Calculation that estimates the kidney filtration rate.', 'mL/min/1.73m²'),
    (60, 6, 'Total Calcium', '', 'Total calcium, including both free (ionized) and protein-bound (mainly to albumin) calcium.', 'mg/dL'),
    (61, 6, 'Ionized Calcium', '', 'Measures biologically active, unbound calcium.', 'mmol/L'),
    (62, 7, 'Magnesium', '', 'Serum concentration of magnesium that is an indicator of kidney health.', 'mg/dL'),
    (63, 8, 'Vitamin D', '', 'A vitamin critical for regulating mineral levels, supporting immune function, and influencing muscle function and mood.', 'ng/mL'),
    (64, 9, 'Parathyroid Hormone (PTH)', 'PTH', 'A hormone that is critical for calcium balance, bone health, and parathyroid gland function.', 'pg/mL'),
    (65, 10, 'White Blood Cell (RBC) count', 'WBC', 'Total number of white blood cells.', '×10³/µL'),
    (66, 10, 'Red Blood Cell (RBC) count', 'RBC', 'Total number of red blood cells.', '×10⁶/µL'),
    (67, 10, 'Hemoglobin (HGB)', 'HGB', 'Oxygen-carrying protein in red blood cells.', 'g/dL'),
    (68, 10, 'Hematocrit (HCT)', 'HCT', 'Percentage of blood volume made up of red blood cells.', '%'),
    (69, 10, 'Mean Corpuscular Volume (MCV)', 'MCV', 'Average size of red blood cells.', 'fL'),
    (70, 10, 'Mean Corpuscular Hemoglobin (MCH)', 'MCH', 'Average hemoglobin per red blood cell.', 'pg'),
    (71, 10, 'Mean Corpuscular Hemoglobin Concentration (MCHC)', 'MCHC', 'Hemoglobin concentration per red blood cell.', 'g/dL'),
    (72, 10, 'Red Cell Distribution Width (RDW)', 'RDW', 'Variation in red blood cell size.', '%'),
    (73, 10, 'Reticulocyte Count', '', 'Number of immature red blood cells recently released from bone marrow.', '%'),
    (74, 10, 'Platelet (PLT) Count', 'PLT', 'Total number of platelets.', '×10³/µL'),
    (75, 10, 'Mean Platelet Volume (MPV)', 'MPV', 'Average platelet size.', 'fL'),
    (76, 10, 'Neutrophils (NEU)', 'NEU', 'White blood cells specialized for bacterial infections and inflammation.', '×10³/µL'),
    (77, 10, 'Lymphocytes (LYMPH)', 'LYMPH', 'White blood cells specialized for viral infections and some leukemias.', '×10³/µL'),
    (78, 10, 'Monocytes (MONO)', 'MONO', 'White blood cells specialized for chronic infection and recovery phase.', '×10³/µL'),
    (79, 10, 'Eosinophils (EOS)', 'EOS', 'White blood cells specialized for allergies, asthma, and parasitic infections.', '×10³/µL'),
    (80, 10, 'Basophils (BASO)', 'BASO', 'White blood cells specialized for allergic reactions and chronic inflammation.', '×10³/µL'),
    (81, 10, 'Absolute Immature Granulocytes (ImmGrn)', 'ImmGrn', 'Early-stage white blood cells that have not fully matured.', '×10³/µL'),
    (82, 10, 'Nucleated Red Blood Cell (NRBC)', 'NRBC', 'Immature red blood cells that still contain a nucleus.', '×10³/µL'),
    (83, 10, 'Neutrophil %', 'NEU %', 'Concentration of neutrophils in blood.', '%'),
    (84, 10, 'Lymphocyte %', 'LYMPH %', 'Concentration of lymphocytes in blood.', '%'),
    (85, 10, 'Monocyte %', 'MONO %', 'Concentration of monocytes in blood.', '%'),
    (86, 10, 'Eosinophil %', 'EOS %', 'Concentration of eosinophils in blood.', '%'),
    (87, 10, 'Basophil %', 'BASO %', 'Concentration of basophils in blood.', '%'),
    (88, 10, 'Absolute Immature Granulocyte (ImmGrn) %', 'ImmGrn %', 'Concentration of immature granulocytes in blood.', '%'),
    (89, 11, 'Thyroid Stimulating Hormone (TSH)', 'TSH', 'Hormone produced by the pituitary gland that regulates T3 and T4 production.', 'µIU/mL'),
    (90, 11, 'Free Thyroxine (T4)', 'Free T4', 'Unbound, biologically active T4 hormone in blood.', 'ng/dL'),
    (91, 11, 'Total Thyroxine (T4)', 'TOTAL T4', 'Total thyroxine hormone including bound and unbound.', 'µg/dL'),
    (92, 11, 'Free Triiodothyronine (T3)', 'Free T3', 'Unbound, biologically active T3 hormone in blood.', 'pg/mL'),
    (93, 11, 'Total Triiodothyronine (T3)', 'TOTAL T3', 'Total triiodothyronine hormone including bound and unbound.', 'ng/dL'),
    (94, 12, 'Thyroid Peroxidase Antibodies (TPO)', 'TPO', 'Autoantibodies against thyroid peroxidase enzyme.', 'IU/mL'),
    (95, 12, 'Thyroglobulin Antibodies (TGA)', 'TGA', 'Autoantibodies against thyroglobulin protein.', 'IU/mL'),
    (96, 12, 'Thyroid Stimulating Immunoglobulin (TSI)', 'TSI', 'Autoantibodie that causes the thyroid to overproduce hormones.', '%'),
    (97, 13, 'Dehydroepiandrosterone Sulfate (DHEA Sulfate)', 'DHEA Sulfate', 'Hormone produced by the adrenal glands that is used to make androgens and estrogens.', 'µg/dL'),
    (98, 14, 'C-Reactive Protein (CRP)', 'CRP', 'A protein made by the liver in response to inflammation.', 'mg/L'),
    (99, 15, 'Sed Rate (ESR)', 'ESR', 'Measures the level of protein-bound red blood cells to measure inflammation.', 'mm/hr'),
    (100, 16, 'Sex Hormone Binding Globulin (SHBG)', 'SHBG', 'A protein made by the liver that binds to sex hormones.', 'nmol/L'),
    (101, 17, 'Total Testosterone', '', 'Measures the testosterone bound to SHBG, albumin, and free testosterone.', 'ng/dL'),
    (102, 17, 'Free Testosterone', '', 'Measures only unbound, biologically active testosterone.', 'pg/mL'),
    (103, 18, 'Plasma Homocysteine', '', 'Measures how much homocysteine is circulating in the blood.', 'µmol/L'),
    (104, 19, 'Follicle Stimulating Hormone (FSH)', 'FSH', 'Measures how much FSH is circulating in the blood to evaluate ovarian function, fertility, and pituitary health.', 'mIU/mL'),
    (105, 20, 'Estradiol (E2)', 'E2', 'Measures the amount of circulating estradiol to evaluate ovarian function, estrogen status, or causes of irregular hormones.', 'pg/mL'),
    (106, 21, 'Luteinizing Hormone (LH)', 'LH', 'Measures how much LH is present and helps assess fertility, reproductive cycles, and pituitary health.', 'mIU/mL'),
    (107, 22, 'Progesterone', '', 'Measures how much progesterone is circulating in the blood to assess ovulation.', 'ng/mL'),
    (108, 23, 'Tissue Transglutaminase Antibody, IgA Class (tTG-IgA)', 'tTG-IgA', 'Measures immunoglobulin A class autoantibodies produced against tissue transglutaminase.', 'U/mL'),
    (109, 23, 'Total Immunoglubulin A (IgA)', 'IgA', 'Measures total immunoglobulin A in blood to identify a deficiency that may affect results of the rest of the panel.', 'mg/dL'),
    (110, 23, 'Tissue Transglutaminase Antibody, IgG Class (tTG-IgG)', 'tTG-IgG', 'Measures immunoglobulin G class autoantibodies produced against tissue transglutaminase.', 'U/mL'),
    (111, 23, 'Deamidated Gliadin Peptide, IgA Class (DGP-IgA)', 'DGP-IgA', 'Measures immunoglobulan A class autoantibodies produced against deamidated gliadin peptides.', 'U/mL'),
    (112, 23, 'Deamidated Gliadin Peptide, IgG Class (DGP-IgG)', 'DGP-IgG', 'Measures immunoglobulan G class autoantibodies produced against deamidated gliadin peptides.', 'U/mL'),
    (113, 23, 'Anti-Endomysial Antibodies (EMA-IgA)', 'EMA-IgA', 'Measures autoantibodies against endomysium, the connective tissue around smooth muscle.', 'Boolean'),
    (114, 24, 'Hemoglobin A1c (HbA1c)', 'HbA1c', 'Measures the percentage of hemoglobin in red blood cells to estimate the average blood glucose levels over the past 2-3 months.', '%'),
    (115, 25, 'Lipoprotein (a)', '', 'Measures the amount of Lp(a) in the blood.', 'mg/dL'),
    (116, 26, 'LDL Particle Number (LDL-P)', '', 'Concentration of low density lipoproteins.', 'nmol/L'),
    (117, 26, 'Small LDL Particle Number (LDL-P)', '', 'Concentration of low density cholesterol.', 'nmol/L'),
    (118, 26, 'LDL Size', '', 'Size of low density cholesterol.', 'nmol/L'),
    (119, 26, 'HDL-P', '', 'Concentration of high density cholesterol.', 'µmol/L'),
    (120, 26, 'Lipoprotein Insulin Resistance (LP-IR) score', '', 'Composite score using particle data to estimate insulin resistance.', '0-100'),
    (121, 27, 'Serum Iodine', '', 'Concentration of iodine in blood.', 'µg/L'),
    (122, 28, 'Plasma Pyridoxal-5''-Phosphate (PLP)', '', 'Measures the biologically active coenzyme form of B6.', 'µg/L'),
    (123, 29, 'Plasma Riboflavin/Vitamin B2', '', 'Measures concentration of B2 in blood.', 'µg/L'),
    (124, 30, 'Vitamin B12', '', 'The amount of circulating B12 in the blood.', 'pg/mL'),
    (125, 31, 'Folic Acid', '', 'Concentration of folate in blood.', 'ng/mL'),
    (126, 32, 'Unsaturated Vitamin B12 Binding Capacity', '', 'Measures ability of B12 to bind to blood proteins.', 'pg/mL'),
    (127, 32, 'Total Vitamin B12', '', 'The amount of circulating B12 in the blood.', 'pg/mL'),
    (128, 32, 'Saturation', '', '% saturation of B12 in blood.', '%'),
    (129, 33, 'Rheumatoid Factor (RF)', '', 'Concentration of RF autoantibodies in the blood.', 'IU/mL'),
    (130, 34, 'ANA Titer', '', 'Measurement of concentration of ANA antibodies in blood.', 'Ratio of Dilution'),
    (131, 34, 'ANA Pattern', '', 'Describes where in the nucleus the antibodies are binding as seen under a fluorescent microscope.', 'Text Description'),
    (132, 34, 'Impression', '', 'Laboratory interpretation of the results.', 'Text Description'),
    (133, 35, 'Anti-RNP', '', 'Autoantibody associated with mixed connective tissue disease.', 'U/mL'),
    (134, 35, 'Anti-Sm', '', 'Autoantibody specific to systemic lupus erythematosus.', 'U/mL'),
    (135, 35, 'Anti-SS-A (Ro)', '', 'Autoantibody associated with Sjogren''s syndrome, lupus, and congenital heart block.', 'U/mL'),
    (136, 35, 'Anti-SS-B (La)', '', 'Autoantibody associated with Sjogren''s syndrome and lupus.', 'U/mL'),
    (137, 35, 'Anti-Scl-70', '', 'Autoantibody associated with diffuse systemic sclerosis (scleroderma).', 'U/mL'),
    (138, 35, 'Anti-Jo-1', '', 'Autoantibody associated with polymyositis, dermatomyositis, and antisynthetase syndrome.', 'U/mL'),
    (139, 36, 'C3', '', 'Most abunant complement protein in blood that is activated to fight infection and trigger inflammation.', 'mg/dL'),
    (140, 36, 'C4', '', 'Part of the classical complement pathway triggered by immune complexes, it is used to activate C3.', 'mg/dL'),
    (141, 36, 'CH50', '', 'Measure of the overall functional activity of the classical complement pathway.', 'U/mL'),
    (142, 37, 'Serum Iron', '', 'Amount of iron circulating in blood, bound to transferrin.', 'µg/dL'),
    (143, 37, 'Total Iron-Binding Capacity (TIBC)', '', 'Total capacity of blood to bind to iron (proxy for transferrin level).', 'µg/dL'),
    (144, 37, 'Transferrin Saturation', '', 'Concentration of transferrin binding sites that are bound to iron.', '%'),
    (145, 37, 'Ferritin', '', 'Iron storage protein that reflects the body''s iron reserves.', 'ng/mL');

INSERT INTO test_ranges (test_range_id, test_id, applies_to_gender, min_of_range, max_of_range)
VALUES
    (0, 0, 'N', 70, 99),
    (1, 1, 'N', 8.5, 10.5),
    (2, 2, 'N', 135, 145),
    (3, 3, 'N', 3.5, 5),
    (4, 4, 'N', 22, 29),
    (5, 5, 'N', 96, 106),
    (6, 6, 'N', 7, 20),
    (7, 7, 'N', 0.6, 1.3),
    (8, 8, 'N', 7, 56),
    (9, 9, 'N', 44, 147),
    (10, 10, 'N', 10, 40),
    (11, 11, 'N', 22, 29),
    (12, 12, 'N', 0.1, 1.2),
    (13, 13, 'N', 7, 20),
    (14, 14, 'N', 8.5, 10.5),
    (15, 15, 'N', 96, 106),
    (16, 16, 'N', 0.6, 1.3),
    (17, 17, 'N', 70, 99),
    (18, 18, 'N', 3.5, 5),
    (19, 19, 'N', 135, 145),
    (20, 20, 'N', 6, 8.3),
    (21, 21, 'N', 3.5, 5),
    (22, 22, 'N', 2, 3.5),
    (23, 23, 'N', 1, 2.2),
    (24, 24, 'N', 135, 145),
    (25, 25, 'N', 3.5, 5),
    (26, 26, 'N', 96, 106),
    (27, 27, 'N', 22, 29),
    (28, 28, 'N', 8, 16),
    (29, 29, 'N', 0, 200),
    (30, 30, 'N', 0, 100),
    (31, 31, 'M', 40, NULL),
    (32, 31, 'F', 50, NULL),
    (33, 32, 'N', 0, 150),
    (34, 33, 'N', 5, 40),
    (35, 34, 'N', 0, 5),
    (36, 35, 'N', 10, 40),
    (37, 36, 'N', 7, 56),
    (38, 37, 'N', 44, 147),
    (39, 38, 'N', 0.1, 1.2),
    (40, 39, 'N', 0, 0.3),
    (41, 40, 'N', 0.2, 0.9),
    (42, 41, 'N', 3.5, 5),
    (43, 42, 'M', 8, 61),
    (44, 42, 'F', 5, 36),
    (45, 43, 'N', 0, 15),
    (46, 44, 'N', 6, 8.3),
    (47, 45, 'N', 2, 3.5),
    (48, 46, 'N', 11, 13.5),
    (49, 47, 'N', 140, 280),
    (50, 48, 'N', 70, 99),
    (51, 49, 'N', 2.5, 4.5),
    (52, 50, 'N', 8.5, 10.5),
    (53, 51, 'N', 3.5, 5),
    (54, 52, 'N', 135, 145),
    (55, 53, 'N', 96, 106),
    (56, 54, 'N', 22, 29),
    (57, 55, 'N', 3.5, 5),
    (58, 56, 'N', 0.6, 1.3),
    (59, 57, 'N', 7, 20),
    (60, 58, 'N', 8, 16),
    (61, 59, 'N', 90, NULL),
    (62, 60, 'N', 8.5, 10.5),
    (63, 61, 'N', 1.12, 1.32),
    (64, 62, 'N', 1.7, 2.2),
    (65, 63, 'N', 30, 50),
    (66, 64, 'N', 10, 65),
    (67, 65, 'N', 4, 11),
    (68, 66, 'M', 4.7, 6.1),
    (69, 66, 'F', 4.2, 5.4),
    (70, 67, 'M', 13.5, 17.5),
    (71, 67, 'F', 12, 15.5),
    (72, 68, 'M', 41, 53),
    (73, 68, 'F', 36, 46),
    (74, 69, 'N', 80, 100),
    (75, 70, 'N', 27, 33),
    (76, 71, 'N', 32, 36),
    (77, 72, 'N', 11.5, 14.5),
    (78, 73, 'N', 0.5, 1.5),
    (79, 74, 'N', 150, 450),
    (80, 75, 'N', 7.5, 11.5),
    (81, 76, 'N', 1.5, 7.5),
    (82, 77, 'N', 1, 3),
    (83, 78, 'N', 0.2, 0.8),
    (84, 79, 'N', 0.05, 0.5),
    (85, 80, 'N', 0.02, 0.1),
    (86, 81, 'N', 0, 0.03),
    (87, 82, 'N', 0, 0),
    (88, 83, 'N', 40, 70),
    (89, 84, 'N', 20, 40),
    (90, 85, 'N', 2, 8),
    (91, 86, 'N', 1, 4),
    (92, 87, 'N', 0, 1),
    (93, 88, 'N', 0, 0.4),
    (94, 89, 'N', 0.4, 4),
    (95, 90, 'N', 0.8, 1.8),
    (96, 91, 'N', 4.5, 11.2),
    (97, 92, 'N', 2.3, 4.2),
    (98, 93, 'N', 80, 200),
    (99, 94, 'N', 0, 9),
    (100, 95, 'N', 0, 4),
    (101, 96, 'N', 0, 1.3),
    (102, 97, 'M', 28, 510),
    (103, 97, 'F', 13, 380),
    (104, 98, 'N', 0, 10),
    (105, 99, 'N', 0, 20),
    (106, 100, 'M', 10, 57),
    (107, 100, 'F', 17, 144),
    (108, 101, 'M', 300, 1000),
    (109, 101, 'F', 15, 70),
    (110, 102, 'M', 46, 224),
    (111, 102, 'F', 0.5, 6.4),
    (112, 103, 'N', 5, 15),
    (113, 104, 'M', 1.5, 12.4),
    (114, 104, 'F', 2, 135),
    (115, 105, 'M', 10, 50),
    (116, 105, 'F', 20, 750),
    (117, 106, 'M', 1.5, 9.3),
    (118, 106, 'F', 1.9, 540),
    (119, 107, 'M', 0, 1),
    (120, 107, 'F', 0, 214),
    (121, 108, 'N', 0, 4),
    (122, 109, 'N', 70, 400),
    (123, 110, 'N', 0, 4),
    (124, 111, 'N', 0, 20),
    (125, 112, 'N', 0, 20),
    (126, 113, 'N', 0, 0),
    (127, 114, 'N', 0, 5.7),
    (128, 115, 'N', 0, 30),
    (129, 116, 'N', 0, 1000),
    (130, 117, 'N', 0, 527),
    (131, 118, 'N', 20.5, NULL),
    (132, 119, 'N', 30, NULL),
    (133, 120, 'N', 0, 45),
    (134, 121, 'N', 40, 92),
    (135, 122, 'N', 5, 50),
    (136, 123, 'N', 1, 19),
    (137, 124, 'N', 200, 900),
    (138, 125, 'N', 3, NULL),
    (139, 126, 'N', 725, 1200),
    (140, 127, 'N', 1100, 2000),
    (141, 128, 'N', 20, 60),
    (142, 129, 'N', 0, 14),
    (143, 130, 'N', 0, 0.00625),
    (144, 131, 'N', 0, 0),
    (145, 132, 'N', 0, 0),
    (146, 133, 'N', 0, 20),
    (147, 134, 'N', 0, 20),
    (148, 135, 'N', 0, 20),
    (149, 136, 'N', 0, 20),
    (150, 137, 'N', 0, 20),
    (151, 138, 'N', 0, 20),
    (152, 139, 'N', 90, 180),
    (153, 140, 'N', 10, 40),
    (154, 141, 'N', 41, 90),
    (155, 142, 'M', 65, 175),
    (156, 142, 'F', 50, 170),
    (157, 143, 'N', 250, 450),
    (158, 144, 'N', 20, 50),
    (159, 145, 'M', 30, 400),
    (160, 145, 'F', 13, 150);

INSERT INTO goals (goal_id, goal_name) VALUES
    (0, 'Energy'),
    (1, 'Sleep'),
    (2, 'Cognitive health'),
    (3, 'Fertility'),
    (4, 'Digestion'),
    (5, 'Weight management'),
    (6, 'Pain management');

INSERT INTO symptoms (symptom_id, symptom_name, goal_id) VALUES
    -- Energy (goal_id: 0)
    (0, 'Appetite changes', 0),
    (1, 'Cold hands and feet', 0),
    (2, 'Daytime sleepiness', 0),
    (3, 'Excessive sweating', 0),
    (4, 'Fatigue', 0),
    (5, 'Pale skin', 0),
    (6, 'Recurring infections', 0),

    -- Sleep (goal_id: 1)
    (7, 'Difficulty falling asleep', 1),
    (8, 'Getting up frequently at night to urinate', 1),
    (9, 'Nightmares', 1),
    (10, 'Poor sleep', 1),
    (11, 'Restless sleep', 1),
    (12, 'Snoring', 1),
    (13, 'Waking too early', 1),
    (14, 'Waking up frequently', 1),

    -- Cognitive health (goal_id: 2)
    (15, 'Brain fog', 2),
    (16, 'Cloudy thinking', 2),
    (17, 'Confusion', 2),
    (18, 'Difficulty concentrating', 2),
    (19, 'Memory issues', 2),
    (20, 'Mental fatigue', 2),
    (21, 'Mood problems', 2),
    (22, 'Slow thinking', 2),

    -- Fertility (goal_id: 3)
    (23, 'Difficulty conceiving', 3),
    (24, 'Hair loss', 3),
    (25, 'Hormonal imbalance', 3),
    (26, 'Impotence', 3),
    (27, 'Irregular cycles', 3),
    (28, 'Low libido', 3),
    (29, 'Menstrual pain', 3),
    (30, 'PCOS symptoms', 3),

    -- Digestion (goal_id: 4)
    (31, 'Acid reflux', 4),
    (32, 'Bloating', 4),
    (33, 'Constipation', 4),
    (34, 'Diarrhea', 4),
    (35, 'Gas', 4),
    (36, 'Heartburn', 4),
    (37, 'Hives', 4),
    (38, 'Problem complexions', 4),
    (39, 'Rashes', 4),
    (40, 'Stomach pain', 4),

    -- Weight management (goal_id: 5)
    (41, 'Difficulty losing weight', 5),
    (42, 'Emotional eating', 5),
    (43, 'Food cravings', 5),
    (44, 'Plateau', 5),
    (45, 'Slow metabolism', 5),
    (46, 'Unexplained weight gain', 5),

    -- Pain management (goal_id: 6)
    (47, 'Back pain', 6),
    (48, 'Body aches', 6),
    (49, 'Headaches', 6),
    (50, 'Joint pain or swelling', 6),
    (51, 'Muscle pain or cramps', 6),
    (52, 'Neck pain', 6),
    (53, 'Numbness', 6),
    (54, 'Restless legs', 6),
    (55, 'Tingling', 6);

INSERT INTO test_recommendations (test_recommendation_id, symptom_id, test_id) VALUES
    -- Appetite changes (symptom_id: 0)
    (0, 0, 89),   -- TSH
    (1, 0, 90),   -- Free T4
    (2, 0, 48),   -- Glucose
    (3, 0, 114),  -- HbA1c
    (4, 0, 65),   -- WBC
    (5, 0, 145),  -- Ferritin
    (6, 0, 124),  -- Vitamin B12
    (7, 0, 63),   -- Vitamin D

    -- Cold hands and feet (symptom_id: 1)
    (8, 1, 89),   -- TSH
    (9, 1, 90),   -- Free T4
    (10, 1, 66),  -- RBC
    (11, 1, 67),  -- Hemoglobin
    (12, 1, 145), -- Ferritin
    (13, 1, 124), -- Vitamin B12
    (14, 1, 63),  -- Vitamin D

    -- Daytime sleepiness (symptom_id: 2)
    (15, 2, 89),  -- TSH
    (16, 2, 48),  -- Glucose
    (17, 2, 114), -- HbA1c
    (18, 2, 66),  -- RBC
    (19, 2, 67),  -- Hemoglobin
    (20, 2, 145), -- Ferritin
    (21, 2, 63),  -- Vitamin D
    (22, 2, 124), -- Vitamin B12
    (23, 2, 62),  -- Magnesium

    -- Excessive sweating (symptom_id: 3)
    (24, 3, 89),  -- TSH
    (25, 3, 90),  -- Free T4
    (26, 3, 92),  -- Free T3
    (27, 3, 48),  -- Glucose
    (28, 3, 114), -- HbA1c

    -- Fatigue (symptom_id: 4)
    (29, 4, 89),  -- TSH
    (30, 4, 90),  -- Free T4
    (31, 4, 66),  -- RBC
    (32, 4, 67),  -- Hemoglobin
    (33, 4, 145), -- Ferritin
    (34, 4, 63),  -- Vitamin D
    (35, 4, 124), -- Vitamin B12
    (36, 4, 48),  -- Glucose
    (37, 4, 62),  -- Magnesium
    (38, 4, 98),  -- CRP

    -- Pale skin (symptom_id: 5)
    (39, 5, 66),  -- RBC
    (40, 5, 67),  -- Hemoglobin
    (41, 5, 68),  -- Hematocrit
    (42, 5, 145), -- Ferritin
    (43, 5, 124), -- Vitamin B12
    (44, 5, 125), -- Folic Acid
    (45, 5, 142), -- Serum Iron
    (46, 5, 143), -- TIBC

    -- Recurring infections (symptom_id: 6)
    (47, 6, 65),  -- WBC
    (48, 6, 76),  -- Neutrophils
    (49, 6, 77),  -- Lymphocytes
    (50, 6, 63),  -- Vitamin D
    (51, 6, 109), -- Total IgA
    (52, 6, 48),  -- Glucose
    (53, 6, 114), -- HbA1c

    -- Difficulty falling asleep (symptom_id: 7)
    (54, 7, 89),  -- TSH
    (55, 7, 62),  -- Magnesium
    (56, 7, 63),  -- Vitamin D
    (57, 7, 48),  -- Glucose
    (58, 7, 98),  -- CRP

    -- Getting up frequently at night to urinate (symptom_id: 8)
    (59, 8, 48),  -- Glucose
    (60, 8, 114), -- HbA1c
    (61, 8, 57),  -- BUN
    (62, 8, 56),  -- Creatinine
    (63, 8, 59),  -- eGFR
    (64, 8, 50),  -- Calcium

    -- Nightmares (symptom_id: 9)
    (65, 9, 62),  -- Magnesium
    (66, 9, 48),  -- Glucose
    (67, 9, 63),  -- Vitamin D
    (68, 9, 124), -- Vitamin B12

    -- Poor sleep (symptom_id: 10)
    (69, 10, 89), -- TSH
    (70, 10, 62), -- Magnesium
    (71, 10, 63), -- Vitamin D
    (72, 10, 145),-- Ferritin
    (73, 10, 98), -- CRP

    -- Restless sleep (symptom_id: 11)
    (74, 11, 145),-- Ferritin
    (75, 11, 62), -- Magnesium
    (76, 11, 63), -- Vitamin D
    (77, 11, 124),-- Vitamin B12

    -- Snoring (symptom_id: 12)
    (78, 12, 89), -- TSH
    (79, 12, 29), -- Total Cholesterol
    (80, 12, 32), -- Triglycerides
    (81, 12, 114),-- HbA1c
    (82, 12, 48), -- Glucose

    -- Waking too early (symptom_id: 13)
    (83, 13, 48), -- Glucose
    (84, 13, 89), -- TSH
    (85, 13, 62), -- Magnesium
    (86, 13, 98), -- CRP

    -- Waking up frequently (symptom_id: 14)
    (87, 14, 48), -- Glucose
    (88, 14, 89), -- TSH
    (89, 14, 62), -- Magnesium
    (90, 14, 63), -- Vitamin D

    -- Brain fog (symptom_id: 15)
    (91, 15, 89), -- TSH
    (92, 15, 90), -- Free T4
    (93, 15, 124),-- Vitamin B12
    (94, 15, 63), -- Vitamin D
    (95, 15, 98), -- CRP
    (96, 15, 48), -- Glucose
    (97, 15, 114),-- HbA1c
    (98, 15, 145),-- Ferritin

    -- Cloudy thinking (symptom_id: 16)
    (99, 16, 89), -- TSH
    (100, 16, 124),-- Vitamin B12
    (101, 16, 48), -- Glucose
    (102, 16, 63), -- Vitamin D
    (103, 16, 145),-- Ferritin

    -- Confusion (symptom_id: 17)
    (104, 17, 124),-- Vitamin B12
    (105, 17, 48), -- Glucose
    (106, 17, 89), -- TSH
    (107, 17, 103),-- Homocysteine
    (108, 17, 125),-- Folic Acid
    (109, 17, 24), -- Sodium

    -- Difficulty concentrating (symptom_id: 18)
    (110, 18, 89), -- TSH
    (111, 18, 145),-- Ferritin
    (112, 18, 124),-- Vitamin B12
    (113, 18, 48), -- Glucose
    (114, 18, 63), -- Vitamin D
    (115, 18, 62), -- Magnesium

    -- Memory issues (symptom_id: 19)
    (116, 19, 124),-- Vitamin B12
    (117, 19, 103),-- Homocysteine
    (118, 19, 89), -- TSH
    (119, 19, 125),-- Folic Acid
    (120, 19, 63), -- Vitamin D

    -- Mental fatigue (symptom_id: 20)
    (121, 20, 89), -- TSH
    (122, 20, 145),-- Ferritin
    (123, 20, 124),-- Vitamin B12
    (124, 20, 63), -- Vitamin D
    (125, 20, 98), -- CRP
    (126, 20, 62), -- Magnesium

    -- Mood problems (symptom_id: 21)
    (127, 21, 89), -- TSH
    (128, 21, 63), -- Vitamin D
    (129, 21, 124),-- Vitamin B12
    (130, 21, 125),-- Folic Acid
    (131, 21, 62), -- Magnesium
    (132, 21, 145),-- Ferritin

    -- Slow thinking (symptom_id: 22)
    (133, 22, 89), -- TSH
    (134, 22, 90), -- Free T4
    (135, 22, 124),-- Vitamin B12
    (136, 22, 48), -- Glucose
    (137, 22, 145),-- Ferritin

    -- Difficulty conceiving (symptom_id: 23)
    (138, 23, 104),-- FSH
    (139, 23, 106),-- LH
    (140, 23, 105),-- Estradiol
    (141, 23, 107),-- Progesterone
    (142, 23, 89), -- TSH
    (143, 23, 101),-- Total Testosterone
    (144, 23, 100),-- SHBG
    (145, 23, 63), -- Vitamin D

    -- Hair loss (symptom_id: 24)
    (146, 24, 89), -- TSH
    (147, 24, 90), -- Free T4
    (148, 24, 145),-- Ferritin
    (149, 24, 101),-- Total Testosterone
    (150, 24, 97), -- DHEA Sulfate
    (151, 24, 124),-- Vitamin B12
    (152, 24, 63), -- Vitamin D

    -- Hormonal imbalance (symptom_id: 25)
    (153, 25, 104),-- FSH
    (154, 25, 106),-- LH
    (155, 25, 105),-- Estradiol
    (156, 25, 107),-- Progesterone
    (157, 25, 101),-- Total Testosterone
    (158, 25, 89), -- TSH
    (159, 25, 100),-- SHBG
    (160, 25, 97), -- DHEA Sulfate

    -- Impotence (symptom_id: 26)
    (161, 26, 101),-- Total Testosterone
    (162, 26, 102),-- Free Testosterone
    (163, 26, 89), -- TSH
    (164, 26, 48), -- Glucose
    (165, 26, 114),-- HbA1c
    (166, 26, 100),-- SHBG
    (167, 26, 29), -- Total Cholesterol
    (168, 26, 30), -- LDL

    -- Irregular cycles (symptom_id: 27)
    (169, 27, 104),-- FSH
    (170, 27, 106),-- LH
    (171, 27, 105),-- Estradiol
    (172, 27, 107),-- Progesterone
    (173, 27, 89), -- TSH
    (174, 27, 101),-- Total Testosterone
    (175, 27, 100),-- SHBG

    -- Low libido (symptom_id: 28)
    (176, 28, 101),-- Total Testosterone
    (177, 28, 102),-- Free Testosterone
    (178, 28, 89), -- TSH
    (179, 28, 100),-- SHBG
    (180, 28, 97), -- DHEA Sulfate
    (181, 28, 63), -- Vitamin D

    -- Menstrual pain (symptom_id: 29)
    (182, 29, 105),-- Estradiol
    (183, 29, 107),-- Progesterone
    (184, 29, 98), -- CRP
    (185, 29, 89), -- TSH
    (186, 29, 63), -- Vitamin D
    (187, 29, 62), -- Magnesium

    -- PCOS symptoms (symptom_id: 30)
    (188, 30, 101),-- Total Testosterone
    (189, 30, 102),-- Free Testosterone
    (190, 30, 104),-- FSH
    (191, 30, 106),-- LH
    (192, 30, 48), -- Glucose
    (193, 30, 114),-- HbA1c
    (194, 30, 29), -- Total Cholesterol
    (195, 30, 32), -- Triglycerides
    (196, 30, 100),-- SHBG
    (197, 30, 97), -- DHEA Sulfate

    -- Acid reflux (symptom_id: 31)
    (198, 31, 62), -- Magnesium
    (199, 31, 50), -- Calcium
    (200, 31, 108),-- tTG-IgA
    (201, 31, 109),-- IgA

    -- Bloating (symptom_id: 32)
    (202, 32, 108),-- tTG-IgA
    (203, 32, 109),-- IgA
    (204, 32, 110),-- tTG-IgG
    (205, 32, 98), -- CRP
    (206, 32, 89), -- TSH

    -- Constipation (symptom_id: 33)
    (207, 33, 89), -- TSH
    (208, 33, 62), -- Magnesium
    (209, 33, 50), -- Calcium
    (210, 33, 51), -- Potassium
    (211, 33, 63), -- Vitamin D

    -- Diarrhea (symptom_id: 34)
    (212, 34, 108),-- tTG-IgA
    (213, 34, 109),-- IgA
    (214, 34, 89), -- TSH
    (215, 34, 98), -- CRP
    (216, 34, 99), -- ESR

    -- Gas (symptom_id: 35)
    (217, 35, 108),-- tTG-IgA
    (218, 35, 109),-- IgA
    (219, 35, 111),-- DGP-IgA
    (220, 35, 112),-- DGP-IgG

    -- Heartburn (symptom_id: 36)
    (221, 36, 62), -- Magnesium
    (222, 36, 50), -- Calcium
    (223, 36, 98), -- CRP

    -- Hives (symptom_id: 37)
    (224, 37, 79), -- Eosinophils
    (225, 37, 98), -- CRP
    (226, 37, 109),-- IgA
    (227, 37, 89), -- TSH

    -- Problem complexions (symptom_id: 38)
    (228, 38, 98), -- CRP
    (229, 38, 101),-- Total Testosterone
    (230, 38, 63), -- Vitamin D
    (231, 38, 89), -- TSH
    (232, 38, 145),-- Ferritin

    -- Rashes (symptom_id: 39)
    (233, 39, 79), -- Eosinophils
    (234, 39, 98), -- CRP
    (235, 39, 108),-- tTG-IgA
    (236, 39, 109),-- IgA

    -- Stomach pain (symptom_id: 40)
    (237, 40, 108),-- tTG-IgA
    (238, 40, 109),-- IgA
    (239, 40, 98), -- CRP
    (240, 40, 8),  -- ALT
    (241, 40, 10), -- AST
    (242, 40, 47), -- LDH

    -- Difficulty losing weight (symptom_id: 41)
    (243, 41, 89), -- TSH
    (244, 41, 90), -- Free T4
    (245, 41, 48), -- Glucose
    (246, 41, 114),-- HbA1c
    (247, 41, 29), -- Total Cholesterol
    (248, 41, 32), -- Triglycerides
    (249, 41, 98), -- CRP

    -- Emotional eating (symptom_id: 42)
    (250, 42, 48), -- Glucose
    (251, 42, 114),-- HbA1c
    (252, 42, 89), -- TSH
    (253, 42, 63), -- Vitamin D

    -- Food cravings (symptom_id: 43)
    (254, 43, 48), -- Glucose
    (255, 43, 114),-- HbA1c
    (256, 43, 89), -- TSH
    (257, 43, 62), -- Magnesium

    -- Plateau (symptom_id: 44)
    (258, 44, 89), -- TSH
    (259, 44, 90), -- Free T4
    (260, 44, 92), -- Free T3
    (261, 44, 48), -- Glucose
    (262, 44, 114),-- HbA1c

    -- Slow metabolism (symptom_id: 45)
    (263, 45, 89), -- TSH
    (264, 45, 90), -- Free T4
    (265, 45, 92), -- Free T3
    (266, 45, 93), -- Total T3
    (267, 45, 145),-- Ferritin

    -- Unexplained weight gain (symptom_id: 46)
    (268, 46, 89), -- TSH
    (269, 46, 90), -- Free T4
    (270, 46, 48), -- Glucose
    (271, 46, 114),-- HbA1c
    (272, 46, 29), -- Total Cholesterol
    (273, 46, 32), -- Triglycerides
    (274, 46, 98), -- CRP

    -- Back pain (symptom_id: 47)
    (275, 47, 98), -- CRP
    (276, 47, 63), -- Vitamin D
    (277, 47, 50), -- Calcium
    (278, 47, 99), -- ESR
    (279, 47, 62), -- Magnesium

    -- Body aches (symptom_id: 48)
    (280, 48, 98), -- CRP
    (281, 48, 99), -- ESR
    (282, 48, 63), -- Vitamin D
    (283, 48, 89), -- TSH
    (284, 48, 62), -- Magnesium

    -- Headaches (symptom_id: 49)
    (285, 49, 62), -- Magnesium
    (286, 49, 124),-- Vitamin B12
    (287, 49, 98), -- CRP
    (288, 49, 48), -- Glucose
    (289, 49, 63), -- Vitamin D

    -- Joint pain or swelling (symptom_id: 50)
    (290, 50, 98), -- CRP
    (291, 50, 99), -- ESR
    (292, 50, 129),-- Rheumatoid Factor
    (293, 50, 130),-- ANA Titer
    (294, 50, 63), -- Vitamin D
    (295, 50, 50), -- Calcium

    -- Muscle pain or cramps (symptom_id: 51)
    (296, 51, 62), -- Magnesium
    (297, 51, 50), -- Calcium
    (298, 51, 51), -- Potassium
    (299, 51, 63), -- Vitamin D
    (300, 51, 98), -- CRP
    (301, 51, 89), -- TSH

    -- Neck pain (symptom_id: 52)
    (302, 52, 98), -- CRP
    (303, 52, 99), -- ESR
    (304, 52, 63), -- Vitamin D
    (305, 52, 62), -- Magnesium

    -- Numbness (symptom_id: 53)
    (306, 53, 124),-- Vitamin B12
    (307, 53, 125),-- Folic Acid
    (308, 53, 48), -- Glucose
    (309, 53, 114),-- HbA1c
    (310, 53, 62), -- Magnesium
    (311, 53, 63), -- Vitamin D

    -- Restless legs (symptom_id: 54)
    (312, 54, 145),-- Ferritin
    (313, 54, 62), -- Magnesium
    (314, 54, 63), -- Vitamin D
    (315, 54, 124),-- Vitamin B12

    -- Tingling (symptom_id: 55)
    (316, 55, 124),-- Vitamin B12
    (317, 55, 125),-- Folic Acid
    (318, 55, 48), -- Glucose
    (319, 55, 114),-- HbA1c
    (320, 55, 103),-- Homocysteine
    (321, 55, 62); -- Magnesium

INSERT INTO continents (continent_id ,continent_name) VALUES
    (1,'African'),
    (2,'Asian/Pacific Islander'),
    (3,'European'),
    (4,'North American'),
    (5,'South American'),
    (6,'Middle Eastern'),
    (7,'Caribbean');

INSERT INTO ethnicities (ethnicity_id, continent_id, ethnicity_name) VALUES
    -- African regions (continent_id = 1)
    (1, 1, 'North African'),
    (2, 1, 'West African'),
    (3, 1, 'East African'),
    (4, 1, 'Central African'),
    (5, 1, 'Southern African'),

    -- Asian/Pacific Islander regions (continent_id = 2)
    (6, 2, 'East Asian'),
    (7, 2, 'Southeast Asian'),
    (8, 2, 'South Asian'),
    (9, 2, 'Central Asian'),
    (10, 2, 'Pacific Islander'),

    -- European regions (continent_id = 3)
    (11, 3, 'Northern European'),
    (12, 3, 'Western European'),
    (13, 3, 'Southern European'),
    (14, 3, 'Eastern European'),

    -- North American regions (continent_id = 4)
    (15, 4, 'United States'),
    (16, 4, 'Canadian'),
    (17, 4, 'Mexican'),
    (18, 4, 'Central American'),

    -- South American regions (continent_id = 5)
    (19, 5, 'Brazilian'),
    (20, 5, 'Andean'),
    (21, 5, 'Southern Cone'),
    (22, 5, 'Caribbean South American'),

    -- Middle Eastern regions (continent_id = 6)
    (23, 6, 'Arab'),
    (24, 6, 'Persian'),
    (25, 6, 'Turkish'),
    (26, 6, 'Israeli'),

    -- Caribbean regions (continent_id = 7)
    (27, 7, 'West Indian'),
    (28, 7, 'Afro-Caribbean'),
    (29, 7, 'Indo-Caribbean');


INSERT INTO alert_definitions (alert_category, alert_title, alert_text)

  VALUES
  
    ('Videos', 'Videos Available', 'One or more introductory videos explaining the WEE Protocol are available in the Resource Library.'),
    ('Goals', 'Goal Workflow', 'Patient review and goals define.');

INSERT INTO resource_definitions (resource_category, resource_title, resource_description,
file_name)

  VALUES

    ('Introduction', 'WEE Description', 'WEE Description', 'untitled_design.mp4'),
    ('Introduction', 'WEE Tutorial', 'WEE Tutorial', 'demo_video.mp4'),
    ('Introduction', 'Better Health in 120 Days', 'Finding Answers with the WWE Protocol', '120_days_overview.mp4'),
    ('Introduction', 'Omega 3 & 6', 'Managing Omega 3 & 6', 'omega_3_6.mp4'),
    ('Introduction', 'Toxic Foods Part 1', 'Avoiding Toxic Foods & Additives', 'toxic_foods_part_1.mp4'),
    ('Introduction', 'Toxic Foods Part 2', 'Avoiding Toxic Foods & Additives', 'toxic_foods_part_2.mp4'),
    ('Introduction', 'Toxic Foods Part 3', 'Avoiding Toxic Foods & Additives', 'toxic_foods_part_3.mp4'),
    ('Introduction', 'Celiac Spectrum', 'Celiac Spectrum Workshop', 'celiac.mp4'),
    ('Introduction', 'Carrageenan', 'Carrageenan Online Buying Guide', 'carrageenan.mp4'),
    ('Introduction', 'KIM-2 Installation and Usage', 'Installing & Using KIM-2 Software', 'install_kim_2.mp4'),
    ('Introduction', 'KIM-2 Workshop', 'Use of KIM-2 Workshop', 'kim_2.mp4'),
    ('Introduction', 'Celiac Spectrum', 'Celiac Spectrum Workshop', 'celiac.mp4'),
    ('Introduction', 'HealthSpan - Patients', 'HealthSpan - Patients', 'b2c_pitch.pptx'),
    ('Introduction', 'HealthSpan - Healthcare Insurers', 'HealthSpan - Healthcare Insurers', 'b2b_pitch.pptx');


INSERT INTO alerts (user_id, alert_id, is_active, viewed_date)
  SELECT u.user_id, a.alert_id, TRUE, NULL
    FROM users u
      CROSS JOIN alert_definitions a;

INSERT INTO user_resources (user_id, resource_id, viewed_date)
  SELECT u.user_id, r.resource_id, NULL
    FROM users u
      CROSS JOIN resource_definitions r

