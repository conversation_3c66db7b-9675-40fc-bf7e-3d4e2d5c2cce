
-- WARNING!!! -- WARNING!!! -- WARNING!!!

-- DO NOT EVER RUN THIS FILE ON A PRODUCTION DATABASE!!!

-- This file creates an empty database that allows a developer to begin work
-- with a fresh database. If the database and associated tables already exist,
-- THEY WILL BE UNMERCIFULLY, CALLOUSLY, COLD-BLOODEDLY DESTROYED!!!

-- WARNING!!! -- WARNING!!! -- WARNING!!!

DROP DATABASE IF EXISTS demo WITH (FORCE);

CREATE DATABASE demo WITH OWNER = 'noura';

\connect demo

SET role noura;

COMMENT ON DATABASE demo IS 'This is the db for the Noura WEE Protocol application.';

CREATE EXTENSION IF NOT EXISTS pgcrypto;

CREATE TABLE roles (
  role_id INTEGER PRIMARY KEY,
  role_name VARCHAR(32) NOT NULL
);

COMMENT ON TABLE roles IS 'This table defines the list of allowed roles.';

CREATE TYPE comm_method AS ENUM ('EMAIL', 'SMS');

CREATE TABLE users (
  user_id SERIAL PRIMARY KEY,
  login_id VARCHAR(256) NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT false,
  password VARCHAR(60) NOT NULL,
  first_name VARCHAR(255) NOT NULL,
  middle_name VARCHAR(255) NULL,
  last_name VARCHAR(255) NOT NULL,
  title VARCHAR(255) NULL,
  birth_date DATE NOT NULL,
  gender CHAR(1) NOT NULL,
  email VARCHAR(256) NOT NULL,
  phone VARCHAR(256) NOT NULL,
  address_1 VARCHAR(256) NOT NULL,
  address_2 VARCHAR(256) NULL,
  city VARCHAR(256) NOT NULL,
  state CHAR(2) NOT NULL,
  role_id INTEGER NOT NULL,
  preferred_comm_method comm_method DEFAULT 'email',

  -- Audit columns
  created_date DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_login_date DATE NULL,
  user_version INTEGER NOT NULL DEFAULT 0,
  last_change_date DATE NOT NULL DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT ck_users_login_id UNIQUE (login_id),
  CONSTRAINT ck_users_email UNIQUE (email),
  CONSTRAINT fk_users_roles
    FOREIGN KEY (role_id) REFERENCES roles (role_id),
  CONSTRAINT ck_users_last_login_date
    CHECK (last_login_date IS NULL OR last_login_date >= created_date)
);

CREATE INDEX ix_users_login_id ON users (login_id);
  
COMMENT ON TABLE users IS 'This table has one row for each user, either admin or patient.';

CREATE TABLE continents (
  continent_id INTEGER PRIMARY KEY,
  continent_name VARCHAR(100) NOT NULL UNIQUE
);

COMMENT ON TABLE continents IS 'Stores continent names for the ethnicity selection hierarchy';

CREATE TABLE ethnicities (
  ethnicity_id INTEGER PRIMARY KEY,
  ethnicity_name VARCHAR(256) NOT NULL,
  continent_id INTEGER NOT NULL,
  UNIQUE(continent_id, ethnicity_name),
  CONSTRAINT fk_ethnicities_continents
      FOREIGN KEY (continent_id) REFERENCES continents (continent_id)
);

COMMENT ON TABLE ethnicities IS 'This table has one row for each potential ethnicity. The ethnicity_name column will use the pgsql ltree for hierarchical definition and access.';

CREATE TABLE user_ethnicities (
  user_id INTEGER NOT NULL,
  ethnicity_id INTEGER NOT NULL,
  is_current BOOLEAN DEFAULT TRUE,
  version INTEGER NOT NULL,
  date_reported DATE NOT NULL,
  CONSTRAINT pk_user_ethnicities
    PRIMARY KEY (user_id, ethnicity_id, version),
  CONSTRAINT fk_user_ethnicities_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_user_ethnicities_ethnicity
    FOREIGN KEY (ethnicity_id) REFERENCES ethnicities (ethnicity_id),
  CONSTRAINT version_min
      CHECK (version > 0)
); 

COMMENT ON TABLE user_ethnicities IS 'This table matches users to their list of ethnicities.';

CREATE TABLE user_measurements (
  user_id INTEGER NOT NULL,
  height_feet INTEGER NOT NULL,
  height_inches INTEGER NOT NULL,
  weight_pounds INTEGER NOT NULL,
  is_current BOOLEAN DEFAULT TRUE,
  version INTEGER NOT NULL,
  date_reported DATE NOT NULL,
  CONSTRAINT pk_user_measurements
      PRIMARY KEY (user_id, version),
  CONSTRAINT fk_user_measurements_user
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT version_min
      CHECK (version > 0)
);

COMMENT ON TABLE user_measurements IS 'This table store user measurements.';

CREATE TABLE test_groups (
  test_group_id INTEGER PRIMARY KEY,
  test_group_name VARCHAR(256) NOT NULL,
  test_group_description VARCHAR(8192) NOT NULL
);

COMMENT ON TABLE test_groups IS 'Tests may provide multiple name/value pairs using test_groups.';
  
CREATE TABLE test_definitions (
  test_id INTEGER PRIMARY KEY,
  test_group_id INTEGER NOT NULL,
  test_name VARCHAR(256) NOT NULL,
  test_name_abbreviation VARCHAR(64),
  test_description VARCHAR NOT NULL, 
  test_unit VARCHAR(32) NOT NULL,
  CONSTRAINT fk_test_definitions_group
    FOREIGN KEY (test_group_id) REFERENCES test_groups (test_group_id)
);

COMMENT ON TABLE test_definitions IS 'This table defines the available tests.';

CREATE TABLE test_ranges (
  test_range_id INTEGER PRIMARY KEY,
  test_id INTEGER NOT NULL,
  applies_to_gender CHAR(1) NOT NULL DEFAULT 'N',
  applies_to_ethnicity INTEGER NULL,
  applies_to_age_min INTEGER NULL,
  applies_to_age_max INTEGER NULL,
  min_of_range REAL NULL,
  max_of_range REAL NULL,
  CONSTRAINT fk_test_ranges_test_id
    FOREIGN KEY (test_id) REFERENCES test_definitions (test_id),
  CONSTRAINT fk_test_ranges_ethnicity
    FOREIGN KEY (applies_to_ethnicity) REFERENCES ethnicities (ethnicity_id),
  CONSTRAINT ck_test_ranges_gender
    CHECK (applies_to_gender = 'M' OR applies_to_gender = 'F'
      OR applies_to_gender = 'N'),
  CONSTRAINT ck_test_ranges_min_max
    CHECK ((min_of_range IS NULL AND max_of_range IS NOT NULL)
      OR (min_of_range IS NOT NULL AND max_of_range IS NULL)
      OR (min_of_range IS NOT NULL AND max_of_range IS NOT NULL))
);

CREATE TABLE lab_results (
    lab_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    test_group_id INTEGER NOT NULL,
    lab_date DATE NOT NULL,
    has_abnormal_results BOOLEAN DEFAULT FALSE,
    CONSTRAINT fk_lab_results_user
     FOREIGN KEY (user_id) REFERENCES users (user_id),
    CONSTRAINT fk_lab_results_test_group
     FOREIGN KEY (test_group_id) REFERENCES test_groups (test_group_id)
);

CREATE INDEX idx_lab_results_user_date ON lab_results(user_id, lab_date DESC);

CREATE TABLE test_results (
    lab_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    test_date DATE NOT NULL,
    test_value REAL,
    test_value_text TEXT,
    CHECK (
        (test_value IS NOT NULL AND test_value_text IS NULL) OR
        (test_value IS NULL AND test_value_text IS NOT NULL)
        ),
    CONSTRAINT pk_test_results
        PRIMARY KEY (lab_id, test_id),
    CONSTRAINT fk_test_results_lab
        FOREIGN KEY (lab_id) REFERENCES lab_results (lab_id) ON DELETE CASCADE,
    CONSTRAINT fk_test_results_test
        FOREIGN KEY (test_id) REFERENCES test_definitions (test_id)
);

CREATE INDEX idx_test_results_lab ON test_results(lab_id);

COMMENT ON TABLE test_results IS 'This table matches users to their test results.';

CREATE TABLE goals (
  goal_id INTEGER PRIMARY KEY,
  goal_name VARCHAR(256) NOT NULL
);

COMMENT ON TABLE goals IS 'This table defines the list of possible patient goals.';

CREATE TABLE user_goals (
  user_id INTEGER NOT NULL,
  goal_id INTEGER NOT NULL,
  severity INTEGER NOT NULL DEFAULT 0,
  is_current BOOLEAN DEFAULT TRUE,
  version INTEGER NOT NULL,
  date_reported DATE NOT NULL,
    CONSTRAINT pk_user_goals
      PRIMARY KEY (user_id, goal_id, version),
    CONSTRAINT fk_user_goals_user
      FOREIGN KEY (user_id) REFERENCES users (user_id),
    CONSTRAINT fk_user_goals_goal
      FOREIGN KEY (goal_id) REFERENCES goals (goal_id),
    CONSTRAINT severity_range
      CHECK (severity >= 0 AND severity <= 10),
    CONSTRAINT version_min
      CHECK (version > 0)
);

COMMENT ON TABLE user_goals IS 'This table matches users to their reported goals.';

CREATE TABLE symptoms (
  symptom_id SERIAL PRIMARY KEY,
  symptom_name VARCHAR(255) NOT NULL,
  goal_id INTEGER NOT NULL,
  CONSTRAINT fk_symptom_goal FOREIGN KEY (goal_id)
      REFERENCES goals(goal_id) ON DELETE CASCADE
);

COMMENT ON TABLE symptoms IS 'This table defines the list of possible patient symptoms.';

CREATE TABLE user_symptoms (
  user_id INTEGER NOT NULL,
  symptom_id INTEGER NOT NULL,
  is_current BOOLEAN DEFAULT TRUE,
  version INTEGER NOT NULL,
  date_reported DATE NOT NULL,
  date_ended DATE NULL,
  CONSTRAINT pk_user_symptoms
    PRIMARY KEY (user_id, symptom_id, version),
  CONSTRAINT fk_user_symptoms_user
    FOREIGN KEY (user_id) REFERENCES users (user_id) ,
  CONSTRAINT fk_user_symptoms_symptom
    FOREIGN KEY (symptom_id) REFERENCES symptoms (symptom_id),
  CONSTRAINT version_min
      CHECK (version > 0)
);

COMMENT ON TABLE user_symptoms IS 'This table matches users to their reported symptoms.';

CREATE TABLE test_recommendations (
    test_recommendation_id INT PRIMARY KEY,
    symptom_id INT NOT NULL,
    test_id INT NOT NULL,
    FOREIGN KEY (symptom_id) REFERENCES symptoms(symptom_id),
    FOREIGN KEY (test_id) REFERENCES test_definitions(test_id)
);

COMMENT ON TABLE test_recommendations IS 'This table store test recommendations based on symptoms.';

CREATE TABLE user_test_recommendations (
    user_id INTEGER NOT NULL,
    test_recommendation_id INTEGER NOT NULL,
    is_current BOOLEAN DEFAULT TRUE,
    version INTEGER NOT NULL,
    date_reported DATE NOT NULL,
    CONSTRAINT pk_user_test_recommendations
       PRIMARY KEY (user_id, test_recommendation_id, version),
    CONSTRAINT fk_user_test_recommendations_user
       FOREIGN KEY (user_id) REFERENCES users (user_id) ,
    CONSTRAINT fk_user_test_recommendations_symptom
       FOREIGN KEY (test_recommendation_id) REFERENCES test_recommendations (test_recommendation_id),
    CONSTRAINT version_min
       CHECK (version > 0)
);

COMMENT ON TABLE user_test_recommendations IS 'This table matches users to their test recommendations.';

CREATE TABLE protocols (
  protocol_id INTEGER PRIMARY KEY,
  protocol_name VARCHAR(256) NOT NULL
);

CREATE TABLE symptoms_to_protocols (
  symptom_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_symptoms_to_protocol
    PRIMARY KEY (symptom_id, protocol_id),
  CONSTRAINT fk_symptoms_to_protocols_symptom 
    FOREIGN KEY (symptom_id) REFERENCES symptoms (symptom_id),
  CONSTRAINT fk_symptoms_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE ethnicities_to_protocols (
  ethnicity_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_ethnicities_to_protocol
    PRIMARY KEY (ethnicity_id, protocol_id),
  CONSTRAINT fk_ethnicities_to_protocols_ethnicity 
    FOREIGN KEY (ethnicity_id) REFERENCES ethnicities (ethnicity_id),
  CONSTRAINT fk_ethnicities_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE tests_to_protocols (
  test_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  CONSTRAINT pk_tests_to_protocol
    PRIMARY KEY (test_id, protocol_id),
  CONSTRAINT fk_tests_to_protocols_test 
    FOREIGN KEY (test_id) REFERENCES test_definitions (test_id),
  CONSTRAINT fk_tests_to_protocols_protocol
    FOREIGN KEY (protocol_id) REFERENCES protocols (protocol_id)
);

CREATE TABLE users_to_protocols (
  user_id INTEGER NOT NULL,
  protocol_id INTEGER NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  CONSTRAINT pk_user_to_protocol
    PRIMARY KEY (user_id, protocol_id, start_date),
  CONSTRAINT ck_user_to_protocol_date
    CHECK (end_date IS NULL OR end_date >= start_date)
);

CREATE TABLE alert_definitions (
  alert_id SERIAL PRIMARY KEY,
  alert_category VARCHAR(64) NOT NULL,
  alert_title VARCHAR(64) NOT NULL,
  alert_text VARCHAR NOT NULL
);

CREATE TABLE alerts (
  user_alert_id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  alert_id INTEGER NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  viewed_date DATE NULL,
  CONSTRAINT fk_user_alerts_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_user_alerts_alerts
    FOREIGN KEY (alert_id) REFERENCES alert_definitions (alert_id)
);

CREATE TABLE resource_definitions (
  resource_id SERIAL PRIMARY KEY,
  resource_category VARCHAR(64) NOT NULL,
  resource_title VARCHAR(64) NOT NULL,
  resource_description VARCHAR NOT NULL,
  file_name VARCHAR(2048) NOT NULL
);

CREATE TABLE user_resources (
  user_resource_id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  resource_id INTEGER NOT NULL,
  viewed_date DATE NULL,
  CONSTRAINT fk_resources_user 
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_resources_definitions
    FOREIGN KEY (resource_id) REFERENCES resource_definitions (resource_id)
);

