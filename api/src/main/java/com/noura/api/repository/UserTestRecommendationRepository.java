package com.noura.api.repository;

import com.noura.api.model.entity.UserTestRecommendation;
import com.noura.api.model.entity.UserTestRecommendationId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserTestRecommendationRepository extends JpaRepository<UserTestRecommendation, UserTestRecommendationId> {

    List<UserTestRecommendation> findById_UserIdAndIsCurrentTrue(Integer userId);
    List<UserTestRecommendation> findById_UserIdAndId_Version(Integer userId, Integer version);
}