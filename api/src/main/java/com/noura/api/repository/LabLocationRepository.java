package com.noura.api.repository;

import com.noura.api.model.entity.LabLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LabLocationRepository extends JpaRepository<LabLocation, Integer> {

    List<LabLocation> findByCity(String city);

    List<LabLocation> findByState(String state);

    List<LabLocation> findByCityAndState(String city, String state);

    @Query("SELECT l FROM LabLocation l WHERE LOWER(l.labName) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<LabLocation> searchByName(@Param("name") String name);

    List<LabLocation> findByZipCode(String zipCode);
}