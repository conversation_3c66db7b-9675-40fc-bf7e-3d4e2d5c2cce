package com.noura.api.repository;


import com.noura.api.model.entity.UserAppointment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserAppointmentRepository extends JpaRepository<UserAppointment, Long> {

    // Find user's appointment (each user can only have one)
    Optional<UserAppointment> findByUserId(Integer userId);

    List<UserAppointment> findByStatus(String status);

    @Query("SELECT a FROM UserAppointment a " +
            "LEFT JOIN FETCH a.labLocation " +
            "LEFT JOIN FETCH a.appointmentTests at " +
            "LEFT JOIN FETCH at.testDefinition " +
            "WHERE a.userId = :userId")
    Optional<UserAppointment> findByUserIdWithDetails(@Param("userId") Integer userId);

    @Query("SELECT a FROM UserAppointment a " +
            "LEFT JOIN FETCH a.labLocation " +
            "LEFT JOIN FETCH a.appointmentTests at " +
            "LEFT JOIN FETCH at.testDefinition " +
            "WHERE a.appointmentId = :appointmentId")
    Optional<UserAppointment> findByIdWithDetails(@Param("appointmentId") Long appointmentId);

    List<UserAppointment> findByAppointmentDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT a FROM UserAppointment a WHERE a.labLocation.labId = :labId AND a.appointmentDate >= :fromDate")
    List<UserAppointment> findByLabIdAndDateAfter(@Param("labId") Long labId, @Param("fromDate") LocalDateTime fromDate);
}