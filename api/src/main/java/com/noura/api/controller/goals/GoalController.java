package com.noura.api.controller.goals;

import com.noura.api.dto.request.goals.HealthWizardSubmitRequest;
import com.noura.api.dto.response.goals.HealthWizardDataResponse;
import com.noura.api.dto.response.goals.HealthWizardSubmitResponse;
import com.noura.api.model.entity.User;
import com.noura.api.service.goals.GoalService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/goal")
@RequiredArgsConstructor
@Slf4j
public class GoalController {

    private final GoalService goalService;

    @GetMapping("/data")
    public ResponseEntity<HealthWizardDataResponse> getWizardData(
            @RequestParam(required = false) Integer version, @AuthenticationPrincipal User principal) {

        log.info("GET /goal/data - userId: {}, version: {}", principal.getUserId(), version);

        HealthWizardDataResponse response = goalService.getWizardData(principal.getUserId(), version);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/versions")
    public ResponseEntity<Integer> getUserVersionDates(@AuthenticationPrincipal User principal) {
        log.info("GET /goal/versions/{}", principal.getUserId());

        Integer maxVersion = goalService.getUserMaxVersion(principal.getUserId());
        return ResponseEntity.ok(maxVersion);
    }

    @PostMapping("/submit")
    public ResponseEntity<HealthWizardSubmitResponse> submitWizardData(
            @Valid @RequestBody HealthWizardSubmitRequest request, @AuthenticationPrincipal User principal) {

        request.setUserId(principal.getUserId());

        log.info("POST /goal/submit - userId: {}", request.getUserId());

        HealthWizardSubmitResponse response = goalService.submitWizardData(request);
        return ResponseEntity.ok(response);
    }
}
