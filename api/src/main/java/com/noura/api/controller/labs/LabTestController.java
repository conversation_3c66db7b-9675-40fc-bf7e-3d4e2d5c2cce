package com.noura.api.controller.labs;

import com.noura.api.dto.response.labs.LabResultDetailResponse;
import com.noura.api.dto.response.labs.LabTestResponse;
import com.noura.api.model.entity.User;
import com.noura.api.service.labs.LabTestService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/lab")
@RequiredArgsConstructor
public class LabTestController {

    private final LabTestService labTestService;

    @GetMapping
    public ResponseEntity<List<LabTestResponse>> getLabTests(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String search,
            @AuthenticationPrincipal User principal
    ) {
        List<LabTestResponse> labTests = labTestService.getLabTests(
                principal.getUserId(),
                startDate,
                endDate,
                search
        );

        return ResponseEntity.ok(labTests);
    }

    @GetMapping("/{labId}")
    public ResponseEntity<LabResultDetailResponse> getLabResultDetail(
            @PathVariable Integer labId
    ) {

        LabResultDetailResponse labResult = labTestService.getLabResultDetail(labId);
        return ResponseEntity.ok(labResult);
    }
}