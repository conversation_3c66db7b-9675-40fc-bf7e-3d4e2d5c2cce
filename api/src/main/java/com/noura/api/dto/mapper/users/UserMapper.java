package com.noura.api.dto.mapper.users;

import com.noura.api.dto.request.users.UpdateUserRequest;
import com.noura.api.dto.request.auth.RegisterRequest;
import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.model.entity.User;
import org.mapstruct.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        imports = {LocalDateTime.class}
)
@Component
public interface UserMapper {

    // RESPONSE MAPPINGS
    // ========================================
    @Named("basic")
    @Mapping(target = "id", source = "userId")
    @Mapping(target = "fullName", expression = "java(user.getFirstName() + \" \" + user.getLastName())")
    @Mapping(target = "createdAt", source = "createdDate")
    @Mapping(target = "role", source = "role.roleName")
    UserResponse toResponse(User user);

    @Named("detailed")
    @Mapping(target = "id", source = "userId")
    @Mapping(target = "fullName", expression = "java(getFullName(user.getFirstName(), user.getLastName()))")
    @Mapping(target = "createdAt", source = "createdDate")
    @Mapping(target = "role", source = "role.roleName")
    UserResponse toDetailedResponse(User user);

    // Specify which method to use for the list
    @IterableMapping(qualifiedByName = "basic")
    List<UserResponse> toResponseList(List<User> users);

    // Add a detailed list method if needed
    @IterableMapping(qualifiedByName = "detailed")
    List<UserResponse> toDetailedResponseList(List<User> users);

    // ========================================

    // HELPERS
    // ========================================

    default String getFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null;
        }
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }

    // Auth
    // ========================================

    // ===== ENTITY TO DTO =====

    /**
     * Convert User entity to UserResponse DTO
     */
    @Mapping(target = "id", source = "userId")
    @Mapping(target = "fullName", expression = "java(getFullName(user.getFirstName(), user.getLastName()))")
    @Mapping(target = "phone", source = "phone")
    @Mapping(target = "createdAt", source = "createdDate")
    @Mapping(target = "role", source = "role.roleName")
    UserResponse toUserResponse(User user);

    /**
     * Convert List<User> entities to List<UserResponse> DTOs
     */
    List<UserResponse> toUserResponseList(List<User> users);

    // ===== DTO TO ENTITY =====

    /**
     * Convert RegisterRequest DTO to User entity
     */
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "password", ignore = true) // Password encoded separately for security
    @Mapping(target = "role", ignore = true) // Role will be set separately
    @Mapping(target = "loginId", source = "email")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "middleName", ignore = true)
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "birthDate", ignore = true)
    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "address1", ignore = true)
    @Mapping(target = "address2", ignore = true)
    @Mapping(target = "city", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "phone", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "lastLoginDate", ignore = true)
    @Mapping(target = "userVersion", ignore = true)
    @Mapping(target = "ethnicities", ignore = true)
    @Mapping(target = "symptoms", ignore = true)
    @Mapping(target = "testResults", ignore = true)
    @Mapping(target = "patientProtocols", ignore = true)
    @Mapping(target = "enabled", constant = "true")
    @Mapping(target = "accountNonExpired", constant = "true")
    @Mapping(target = "accountNonLocked", constant = "true")
    @Mapping(target = "credentialsNonExpired", constant = "true")
    User toUser(RegisterRequest registerRequest);

    // ===== UPDATE ENTITY FROM DTO =====

    /**
     * Update existing User entity from UpdateUserRequest DTO
     */
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "loginId", ignore = true)
    @Mapping(target = "email", ignore = true) // Don't allow email updates
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "middleName", ignore = true)
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "birthDate", ignore = true)
    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "address1", ignore = true)
    @Mapping(target = "address2", ignore = true)
    @Mapping(target = "city", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "phone", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "lastLoginDate", ignore = true)
    @Mapping(target = "userVersion", ignore = true)
    @Mapping(target = "ethnicities", ignore = true)
    @Mapping(target = "symptoms", ignore = true)
    @Mapping(target = "testResults", ignore = true)
    @Mapping(target = "patientProtocols", ignore = true)
    @Mapping(target = "enabled", ignore = true)
    @Mapping(target = "accountNonExpired", ignore = true)
    @Mapping(target = "accountNonLocked", ignore = true)
    @Mapping(target = "credentialsNonExpired", ignore = true)
    void updateUserFromRequest(UpdateUserRequest request, @MappingTarget User user);
}
