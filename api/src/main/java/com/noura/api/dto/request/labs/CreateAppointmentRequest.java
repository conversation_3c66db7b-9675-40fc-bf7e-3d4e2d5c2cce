package com.noura.api.dto.request.labs;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateAppointmentRequest {

    @NotNull(message = "User ID is required")
    private Integer userId;

    @NotNull(message = "Lab ID is required")
    private Integer labId;

    @NotNull(message = "Appointment date is required")
    @JsonFormat(pattern = "MM/dd/yyyy HH:mm")
    private LocalDateTime appointmentDate;

    @NotEmpty(message = "At least one test must be selected")
    private List<Integer> testIds;

    private String notes;
}