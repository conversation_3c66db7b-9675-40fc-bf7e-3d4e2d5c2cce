package com.noura.api.dto.response.labs;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAppointmentDTO {

    private Long appointmentId;
    private Long userId;

    @JsonFormat(pattern = "MM/dd/yyyy 'at' hh:mm a")
    private LocalDateTime appointmentDate;

    private LabLocationDTO labLocation;
    private List<TestDTO> tests;
    private String status;
    private String notes;
}