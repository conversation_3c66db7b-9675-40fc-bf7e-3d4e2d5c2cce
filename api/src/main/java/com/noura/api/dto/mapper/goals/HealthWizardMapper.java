package com.noura.api.dto.mapper.goals;

import com.noura.api.dto.response.goals.HeightDTO;
import com.noura.api.model.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface HealthWizardMapper {

    @Mapping(target = "feet", source = "heightFeet")
    @Mapping(target = "inches", source = "heightInches")
    HeightDTO toHeightDTO(UserMeasurement measurement);

    default String mapWeight(UserMeasurement measurement) {
        return measurement != null ? String.valueOf(measurement.getWeightPounds()) : null;
    }

    default String mapContinentName(Continent continent) {
        return continent != null ? continent.getContinentName() : null;
    }

    default String mapEthnicityName(Ethnicity ethnicity) {
        return ethnicity != null ? ethnicity.getEthnicityName() : null;
    }

    default String mapGoalName(Goal goal) {
        return goal != null ? goal.getGoalName() : null;
    }

    default String mapSymptomName(Symptom symptom) {
        return symptom != null ? symptom.getSymptomName() : null;
    }
}