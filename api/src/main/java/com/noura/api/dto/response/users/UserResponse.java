package com.noura.api.dto.response.users;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserResponse {

    private Integer id;
    private String firstName;
    private String middleName;
    private String lastName;
    private String fullName;
    private String title;
    private LocalDate birthDate;
    private String gender;
    private String email;
    private String phone;
    private String address1;
    private String address2;
    private String city;
    private String state;
    private String role;
    private LocalDate createdAt;
    private LocalDate updatedAt;
    private boolean isActive;
}