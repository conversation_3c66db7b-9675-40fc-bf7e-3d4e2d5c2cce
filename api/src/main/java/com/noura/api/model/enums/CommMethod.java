package com.noura.api.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CommMethod {
    EMAIL("EMAIL"),
    SMS("SMS");

    private final String value;

    CommMethod(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static CommMethod fromValue(String value) {
        for (CommMethod method : CommMethod.values()) {
            if (method.value.equalsIgnoreCase(value)) {
                return method;
            }
        }
        throw new IllegalArgumentException("Invalid communication method: " + value);
    }
}