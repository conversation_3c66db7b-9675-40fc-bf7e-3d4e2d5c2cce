package com.noura.api.model.entity;

import java.time.LocalDate;
import java.util.List;
import java.util.ArrayList;
import java.util.Collection;

import com.noura.api.model.enums.CommMethod;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.GrantedAuthority;

@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(exclude = {"ethnicities", "symptoms", "testResults", "patientProtocols"})
@ToString(exclude = {"password", "ethnicities", "symptoms", "testResults", "patientProtocols"})
public class User implements UserDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_id")
    private Integer userId;

    @NotBlank
    @Column(name = "login_id", unique = true, nullable = false)
    private String loginId;

    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = false;

    @NotBlank
    @Column(name = "password", nullable = false)
    private String password;

    @NotBlank
    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Column(name = "middle_name")
    private String middleName;

    @NotBlank
    @Column(name = "last_name", nullable = false)
    private String lastName;

    @Column(name = "title")
    private String title;

    @NotNull
    @Past
    @Column(name = "birth_date", nullable = false)
    private LocalDate birthDate;

    @NotNull
    @Column(name = "gender", nullable = false, length = 1)
    private Character gender;

    @NotBlank
    @Column(name = "address_1", nullable = false)
    private String address1;

    @Column(name = "address_2")
    private String address2;

    @NotBlank
    @Column(name = "city", nullable = false)
    private String city;

    @NotBlank
    @Size(min = 2, max = 2)
    @Column(name = "state", nullable = false, length = 2)
    private String state;

    @NotBlank
    @Email
    @Column(name = "email", unique = true, nullable = false)
    private String email;

    @NotBlank
    @Column(name = "phone", nullable = false)
    private String phone;

    @Column(name = "created_date", nullable = false)
    private LocalDate createdDate;

    @Column(name = "last_login_date")
    private LocalDate lastLoginDate;

    @Version
    @Column(name = "user_version", nullable = false)
    private Integer userVersion;

    @Column(name = "last_change_date", nullable = false)
    private LocalDate lastChangeDate;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @Enumerated(EnumType.STRING)
    @Column(name = "preferred_comm_method", columnDefinition = "comm_method")
    private CommMethod preferredCommMethod;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_ethnicities",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "ethnicity_id")
    )
    @Builder.Default
    private List<Ethnicity> ethnicities = new ArrayList<>();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @Builder.Default
    private List<UserSymptom> symptoms = new ArrayList<>();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @Builder.Default
    private List<LabResult> testResults = new ArrayList<>();

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @Builder.Default
    private List<PatientToProtocol> patientProtocols = new ArrayList<>();

    // Spring Security essential fields only
    @Transient
    private boolean enabled = true;

    @Transient
    private boolean accountNonExpired = true;

    @Transient
    private boolean accountNonLocked = true;

    @Transient
    private boolean credentialsNonExpired = true;

    // JPA lifecycle callbacks
    @PrePersist
    protected void onCreate() {
        if (createdDate == null) {
            createdDate = LocalDate.now();
        }
        if (lastChangeDate == null) {
            lastChangeDate = LocalDate.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        lastChangeDate = LocalDate.now();
    }

    // Custom validation methods
    public void setBirthDate(LocalDate birthDate) {
        if (birthDate != null && birthDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("The birthDate input parameter cannot be in the future.");
        }
        this.birthDate = birthDate;
    }

    public void setLastLoginDate(LocalDate lastLoginDate) {
        if (lastLoginDate != null && createdDate != null && lastLoginDate.isBefore(createdDate)) {
            throw new IllegalArgumentException("The lastLoginDate input parameter must be >= the user's createdDate.");
        }
        this.lastLoginDate = lastLoginDate;
    }

    // UserDetails implementation
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (role == null) {
            return List.of(new SimpleGrantedAuthority("ROLE_USER")); // Default role
        }
        return List.of(new SimpleGrantedAuthority("ROLE_" + role.getRoleName()));
    }

    @Override
    public String getUsername() {
        return email;
    }

    @Override
    public boolean isEnabled() {
        return isActive != null && isActive;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }
}
