package com.noura.api.service.labs;

import com.noura.api.dto.mapper.labs.LabTestMapper;
import com.noura.api.dto.response.labs.*;
import com.noura.api.model.entity.*;
import com.noura.api.repository.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class LabTestService {

    private final LabResultRepository labResultRepository;
    private final LabTestMapper labTestMapper;
    private final TestResultRepository testResultRepository;
    private final TestRangeRepository testRangeRepository;
    private final UserRepository userRepository;
    private final UserEthnicityRepository userEthnicityRepository;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("MMM").withLocale(java.util.Locale.ENGLISH);


    public List<LabTestResponse> getLabTests(
            Integer userId,
            LocalDate startDate,
            LocalDate endDate,
            String search
    ) {
        List<LabResult> labResults;

        if (startDate != null && endDate != null) {
            labResults = labResultRepository.findLabTestsByDateRange(userId, startDate, endDate);
        } else {
            labResults = labResultRepository.findLabTestsByUserId(userId);
        }

        List<LabTestResponse> responses = labTestMapper.toLabTestResponseList(labResults);

        if (search != null && !search.trim().isEmpty()) {
            String searchLower = search.toLowerCase().trim();
            responses = responses.stream()
                    .filter(response -> response.getTitle().toLowerCase().contains(searchLower))
                    .toList();
        }

        return responses;
    }

    public LabResultDetailResponse getLabResultDetail(Integer labId) {
        LabResult labResult = labResultRepository.findByIdWithDetails(labId)
                .orElseThrow(() -> new RuntimeException("Lab result not found with id: " + labId));

        UserTestContext userContext = getUserContext(labResult.getUser().getUserId());

        List<TestMeasurementResponse> measurements = labResult.getTestResults().stream()
                .map(testResult -> buildTestMeasurement(testResult, userContext))
                .collect(Collectors.toList());

        return LabResultDetailResponse.builder()
                .labId(labResult.getLabId())
                .labName(labResult.getTestGroup().getTestGroupName())
                .labDescription(labResult.getTestGroup().getTestGroupDescription())
                .testDate(labResult.getLabDate())
                .laboratoryLocation("Main Laboratory") // TODO: Get from database
                .prescribingDoctor("Dr. Smith") // TODO: Get from database
                .measurements(measurements)
                .build();
    }

    private UserTestContext getUserContext(Integer userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        // Get current ethnicities
        List<UserEthnicity> currentEthnicities = userEthnicityRepository.findCurrentEthnicitiesByUserId(userId);
        List<Integer> ethnicityIds = currentEthnicities.stream()
                .map(ue -> ue.getEthnicity().getEthnicityId())
                .collect(Collectors.toList());

        // Calculate age
        Integer age = null;
        if (user.getBirthDate() != null) {
            age = Period.between(user.getBirthDate(), LocalDate.now()).getYears();
        }

        return UserTestContext.builder()
                .userId(userId)
                .gender(user.getGender() != null ? user.getGender() : 'N')
                .ethnicities(ethnicityIds)
                .age(age)
                .build();
    }

    private TestMeasurementResponse buildTestMeasurement(TestResult currentResult, UserTestContext userContext) {
        Integer testId = currentResult.getId().getTestId();
        LocalDate currentLabDate = currentResult.getTestDate();
        String currentLabDateFormatted = currentLabDate.format(DATE_FORMATTER);

        // Get applicable range for this user and test
        Optional<TestRange> applicableRange = getApplicableRange(testId, userContext);

        // Get history for this test - sorted desc
        List<TestResult> history = testResultRepository.findHistoryByTestIdAndUserId(
                testId,
                userContext.getUserId()
        );

        List<ChartDataPointResponse> chartData = history.stream()
                .sorted(Comparator.comparing(TestResult::getTestDate))
                .map(tr -> ChartDataPointResponse.builder()
                        .month(tr.getTestDate().format(MONTH_FORMATTER).toUpperCase())
                        .value(tr.getTestValue())
                        .date(tr.getTestDate().format(DATE_FORMATTER))
                        .build())
                .collect(Collectors.toList());


        List<HistoryEntryResponse> historyData = history.stream()
                .sorted((tr1, tr2) -> tr2.getTestDate().compareTo(tr1.getTestDate()))
                .map(tr -> HistoryEntryResponse.builder()
                        .date(tr.getTestDate().format(DATE_FORMATTER))
                        .result(tr.getTestValueText() != null ? tr.getTestValueText() :  String.format("%.1f %s",
                                tr.getTestValue(),
                                tr.getTestDefinition().getTestUnit()))
                        .build())
                .collect(Collectors.toList());

        Float minRange = 0f;
        Float maxRange = 100f;
        String normalRangeString = "N/A";
        String status = "normal";

        if (applicableRange.isPresent()) {
            TestRange range = applicableRange.get();
            minRange = range.getMinOfRange() != null ? range.getMinOfRange() : 0f;
            maxRange = range.getMaxOfRange() != null ? range.getMaxOfRange() : 100f;

            normalRangeString = buildNormalRangeString(
                    range.getMinOfRange(),
                    range.getMaxOfRange(),
                    currentResult.getTestDefinition().getTestUnit()
            );

            status = determineStatus(
                    currentResult.getTestValue(),
                    range.getMinOfRange(),
                    range.getMaxOfRange()
            );
        }

        return TestMeasurementResponse.builder()
                .id(testId)
                .name(currentResult.getTestDefinition().getTestName())
                .description(currentResult.getTestDefinition().getTestDescription())
                .value(currentResult.getTestValue())
                .textValue(currentResult.getTestValueText())
                .unit(currentResult.getTestDefinition().getTestUnit())
                .normalRange(normalRangeString)
                .minRange(minRange)
                .maxRange(maxRange)
                .status(status)
                .currentLabDate(currentLabDateFormatted)
                .chartData(chartData)
                .historyData(historyData)
                .build();
    }

    private Optional<TestRange> getApplicableRange(Integer testId, UserTestContext userContext) {
        List<TestRange> ranges;

        if (userContext.getEthnicities() != null && !userContext.getEthnicities().isEmpty()) {
            // Search with ethnicities
            ranges = testRangeRepository.findApplicableRangesWithEthnicities(
                    testId,
                    userContext.getGender(),
                    userContext.getEthnicities(),
                    userContext.getAge()
            );
        } else {
            // Search without ethnicities (only general ranges)
            ranges = testRangeRepository.findApplicableRangesWithoutEthnicities(
                    testId,
                    userContext.getGender(),
                    userContext.getAge()
            );
        }

        // The query already orders by specificity, so we just take the first one
        return ranges.isEmpty() ? Optional.empty() : Optional.of(ranges.get(0));
    }

    private String determineStatus(Float value, Float minRange, Float maxRange) {
        //TODO: missing test_value_text implementation
        if (value == null) {
            return "normal";
        }

        if (minRange == null && maxRange == null) {
            return "normal";
        }

        // Check if abnormal
        if (minRange != null && value < minRange) {
            return "abnormal";
        }
        if (maxRange != null && value > maxRange) {
            return "abnormal";
        }

        // Check if borderline (within 10% of boundaries)
        if (minRange != null && maxRange != null) {
            float range = maxRange - minRange;
            float lowerBorderline = minRange + range * 0.1f;
            float upperBorderline = maxRange - range * 0.1f;

            if (value <= lowerBorderline || value >= upperBorderline) {
                return "borderline";
            }
        } else if (minRange != null) {
            // Only lower bound exists
            float buffer = minRange * 0.1f;
            if (value <= minRange + buffer) {
                return "borderline";
            }
        } else if (maxRange != null) {
            // Only upper bound exists
            float buffer = maxRange * 0.1f;
            if (value >= maxRange - buffer) {
                return "borderline";
            }
        }

        return "normal";
    }

    private String buildNormalRangeString(Float minRange, Float maxRange, String unit) {
        if (minRange == null && maxRange == null) {
            return "N/A";
        }

        if (minRange == null) {
            return String.format("< %s %s", formatValue(maxRange), unit);
        }

        if (maxRange == null) {
            return String.format("> %s %s", formatValue(minRange), unit);
        }

        return String.format("%s-%s %s", formatValue(minRange), formatValue(maxRange), unit);
    }

    private static String formatValue(float value) {
        if (value == 0) {
            return "0.0";
        }

        // Start with high precision and work backwards to remove trailing zeros
        String formatted = String.format("%.6f", value);

        // Remove trailing zeros
        formatted = formatted.replaceAll("0+$", "");

        // Remove trailing decimal point if all decimals were zeros
        formatted = formatted.replaceAll("\\.$", ".0");

        return formatted;
    }
}