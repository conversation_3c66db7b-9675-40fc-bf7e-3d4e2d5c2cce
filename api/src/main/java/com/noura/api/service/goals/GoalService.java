package com.noura.api.service.goals;

import com.noura.api.dto.mapper.goals.HealthWizardMapper;
import com.noura.api.dto.request.goals.HealthWizardSubmitRequest;
import com.noura.api.dto.response.goals.*;
import com.noura.api.model.entity.*;
import com.noura.api.repository.*;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class GoalService {
    private final ContinentRepository continentRepository;
    private final EthnicityRepository ethnicityRepository;
    private final GoalRepository goalRepository;
    private final SymptomRepository symptomRepository;
    private final UserRepository userRepository;
    private final UserMeasurementRepository userMeasurementRepository;
    private final UserEthnicityRepository userEthnicityRepository;
    private final UserGoalRepository userGoalRepository;
    private final UserTestRecommendationRepository userTestRecommendationRepository;
    private final UserSymptomRepository userSymptomRepository;
    private final TestRecommendationRepository testRecommendationRepository;
    private final HealthWizardMapper mapper;

    private static final Map<String, String> GOAL_SYMPTOM_TITLES = Map.of(
            "Energy", "Which of these energy challenges are you facing?",
            "Pain management", "What type of pain are you experiencing?",
            "Sleep", "Which sleep issues are you experiencing?",
            "Cognitive health", "Which cognitive challenges are you facing?",
            "Fertility", "Which fertility concerns do you have?",
            "Digestion", "Which digestive issues are you experiencing?",
            "Weight management", "Which weight management challenges are you facing?"
    );

    // GET method remains the same as before
    public HealthWizardDataResponse getWizardData(Integer userId, Integer version) {
        log.info("Fetching wizard data for user: {}, version: {}", userId, version);

        return HealthWizardDataResponse.builder()
                .continents(getContinentNames())
                .regionsByContinent(getRegionsByContinent())
                .goalOptions(getGoalNames())
                .symptomsByGoal(getSymptomsByGoal())
                .userData(getUserHealthData(userId, version))
                .recommendedLabs(getTestRecommendationsByGoal())
                .build();
    }

    public Integer getUserMaxVersion(Integer userId) {
        log.info("Fetching max version for user: {}", userId);
        return userGoalRepository.findMaxVersionByUserId(userId);
    }

    // POST - Submit wizard data
    public HealthWizardSubmitResponse submitWizardData(HealthWizardSubmitRequest request) {
        log.info("Submitting wizard data for user: {}", request.getUserId());

        try {
            // Verify user exists
            User user = userRepository.findById(request.getUserId())
                    .orElseThrow(() -> new EntityNotFoundException("User not found with id: " + request.getUserId()));

            LocalDate currentDate = LocalDate.now();
            Integer version = userGoalRepository.findNextVersion(request.getUserId());

            // 1. Save measurements
            saveMeasurements(user, request, currentDate, version);

            // 2. Save ethnicity
            saveEthnicity(user, request, currentDate, version);

            // 3. Save goals with severities
            saveGoals(user, request, currentDate, version);

            // 4. Save symptoms
            saveSymptoms(user, request, currentDate, version);

            // 5. Save lab recommendations
            saveLabRecommendations(user, request, currentDate, version);

            log.info("Successfully saved wizard data for user: {}", request.getUserId());

            return HealthWizardSubmitResponse.builder()
                    .success(true)
                    .message("Health wizard data saved successfully")
                    .userId(request.getUserId())
                    .build();

        } catch (EntityNotFoundException e) {
            log.error("Entity not found: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error saving wizard data for user {}: {}", request.getUserId(), e.getMessage(), e);
            throw new RuntimeException("Failed to save health wizard data: " + e.getMessage(), e);
        }
    }

    private void saveMeasurements(User user, HealthWizardSubmitRequest request, LocalDate currentDate, Integer version) {
        // Mark existing measurements as not current
        userMeasurementRepository.findById_UserIdAndIsCurrentTrue(user.getUserId())
                .ifPresent(existing -> {
                    existing.setIsCurrent(false);
                    userMeasurementRepository.save(existing);
                });

        // Create new measurement
        UserMeasurementId id = new UserMeasurementId(user.getUserId(), version);

        UserMeasurement measurement = UserMeasurement.builder()
                .id(id)
                .user(user)
                .heightFeet(request.getHeight().getFeet())
                .heightInches(request.getHeight().getInches())
                .weightPounds(request.getWeight())
                .isCurrent(true)
                .dateReported(currentDate)
                .build();

        userMeasurementRepository.save(measurement);
        log.debug("Saved measurements for user: {}", user.getUserId());
    }

    private void saveEthnicity(User user, HealthWizardSubmitRequest request, LocalDate currentDate, Integer version) {
        // Find the ethnicity by name and continent
        Ethnicity ethnicity = ethnicityRepository
                .findByEthnicityNameAndContinent_ContinentName(request.getEthnicity(), request.getContinent())
                .orElseThrow(() -> new EntityNotFoundException(
                        "Ethnicity not found: " + request.getEthnicity() + " in " + request.getContinent()));

        // Mark existing ethnicities as not current
        userEthnicityRepository.findByUser_UserIdAndIsCurrentTrue(user.getUserId())
                .forEach(existing -> {
                    existing.setIsCurrent(false);
                    userEthnicityRepository.save(existing);
                });

        // Create new ethnicity record
        UserEthnicityId id = new UserEthnicityId(user.getUserId(), ethnicity.getEthnicityId(), version);

        UserEthnicity userEthnicity = UserEthnicity.builder()
                .id(id)
                .user(user)
                .ethnicity(ethnicity)
                .isCurrent(true)
                .dateReported(currentDate)
                .build();

        userEthnicityRepository.save(userEthnicity);
        log.debug("Saved ethnicity for user: {}", user.getUserId());
    }

    private void saveGoals(User user, HealthWizardSubmitRequest request, LocalDate currentDate, Integer version) {
        // Mark existing goals as not current
        userGoalRepository.findByUser_UserIdAndIsCurrentTrue(user.getUserId())
                .forEach(existing -> {
                    existing.setIsCurrent(false);
                    userGoalRepository.save(existing);
                });

        // Save new goals
        for (String goalName : request.getGoals()) {
            Goal goal = goalRepository.findByGoalName(goalName)
                    .orElseThrow(() -> new EntityNotFoundException("Goal not found: " + goalName));

            Integer severity = request.getGoalSeverities().getOrDefault(goalName, 0);

            UserGoalId id = new UserGoalId(user.getUserId(), goal.getGoalId(), version);

            UserGoal userGoal = UserGoal.builder()
                    .id(id)
                    .user(user)
                    .goal(goal)
                    .severity(severity)
                    .isCurrent(true)
                    .dateReported(currentDate)
                    .build();

            userGoalRepository.save(userGoal);
        }
        log.debug("Saved {} goals for user: {}", request.getGoals().size(), user.getUserId());
    }

    private void saveSymptoms(User user, HealthWizardSubmitRequest request, LocalDate currentDate, Integer version) {
        // Mark existing symptoms as not current
        userSymptomRepository.findByUser_UserIdAndIsCurrentTrue(user.getUserId())
                .forEach(existing -> {
                    existing.setIsCurrent(false);
                    existing.setDateEnded(currentDate);
                    userSymptomRepository.save(existing);
                });

        // Map of goal name to symptoms
        Map<String, List<String>> symptomsByGoal = Map.of(
                "Energy", request.getEnergyChallenges() != null ? request.getEnergyChallenges() : List.of(),
                "Pain management", request.getPainTypes() != null ? request.getPainTypes() : List.of(),
                "Sleep", request.getSleepIssues() != null ? request.getSleepIssues() : List.of(),
                "Cognitive health", request.getCognitiveIssues() != null ? request.getCognitiveIssues() : List.of(),
                "Fertility", request.getFertilityIssues() != null ? request.getFertilityIssues() : List.of(),
                "Digestion", request.getDigestionIssues() != null ? request.getDigestionIssues() : List.of(),
                "Weight management", request.getWeightIssues() != null ? request.getWeightIssues() : List.of()
        );

        int totalSymptomsSaved = 0;

        for (Map.Entry<String, List<String>> entry : symptomsByGoal.entrySet()) {
            String goalName = entry.getKey();
            List<String> symptomNames = entry.getValue();

            if (symptomNames.isEmpty()) {
                continue;
            }

            Goal goal = goalRepository.findByGoalName(goalName)
                    .orElseThrow(() -> new EntityNotFoundException("Goal not found: " + goalName));

            for (String symptomName : symptomNames) {
                Symptom symptom = symptomRepository.findBySymptomNameAndGoal(symptomName, goal)
                        .orElseThrow(() -> new EntityNotFoundException(
                                "Symptom not found: " + symptomName + " for goal: " + goalName));

                UserSymptomId id = new UserSymptomId(user.getUserId(), symptom.getSymptomId(), version);

                UserSymptom userSymptom = UserSymptom.builder()
                        .id(id)
                        .user(user)
                        .symptom(symptom)
                        .isCurrent(true)
                        .dateReported(currentDate)
                        .dateEnded(null)
                        .build();

                userSymptomRepository.save(userSymptom);
                totalSymptomsSaved++;
            }
        }
        log.debug("Saved {} symptoms for user: {}", totalSymptomsSaved, user.getUserId());
    }

    private void saveLabRecommendations(User user, HealthWizardSubmitRequest request, LocalDate currentDate, Integer version) {
        // Mark existing recommendations as not current
        userTestRecommendationRepository.findById_UserIdAndIsCurrentTrue(user.getUserId())
                .forEach(existing -> {
                    existing.setIsCurrent(false);
                    userTestRecommendationRepository.save(existing);
                });

        // Collect all symptoms from the request
        List<String> allSymptoms = new ArrayList<>();
        allSymptoms.addAll(request.getEnergyChallenges());
        allSymptoms.addAll(request.getPainTypes());
        allSymptoms.addAll(request.getSleepIssues());
        allSymptoms.addAll(request.getCognitiveIssues());
        allSymptoms.addAll(request.getFertilityIssues());
        allSymptoms.addAll(request.getDigestionIssues());
        allSymptoms.addAll(request.getWeightIssues());

        if (allSymptoms.isEmpty()) {
            log.debug("No symptoms selected, skipping lab recommendations for user: {}", user.getUserId());
            return;
        }

        // Find all symptom entities
        List<Symptom> symptoms = symptomRepository.findBySymptomNameIn(allSymptoms);

        if (symptoms.isEmpty()) {
            log.warn("No matching symptoms found in database for user: {}", user.getUserId());
            return;
        }

        // Get symptom IDs
        List<Integer> symptomIds = symptoms.stream()
                .map(Symptom::getSymptomId)
                .collect(Collectors.toList());

        // Find all test recommendations for these symptoms
        List<TestRecommendation> testRecommendations = testRecommendationRepository.findBySymptomIds(symptomIds);

        // Save new user test recommendations
        for (TestRecommendation testRec : testRecommendations) {
            UserTestRecommendation userTestRec = UserTestRecommendation.builder()
                    .id(new UserTestRecommendationId(user.getUserId(), testRec.getTestRecommendationId(), version))
                    .user(user)
                    .testRecommendation(testRec)
                    .isCurrent(true)
                    .dateReported(currentDate)
                    .build();

            userTestRecommendationRepository.save(userTestRec);
        }

        log.debug("Saved {} test recommendations for user: {}", testRecommendations.size(), user.getUserId());
    }

    // Previous GET methods remain the same
    private List<String> getContinentNames() {
        return continentRepository.findAll().stream()
                .map(Continent::getContinentName)
                .sorted()
                .collect(Collectors.toList());
    }

    private Map<String, List<String>> getRegionsByContinent() {
        List<Continent> continents = continentRepository.findAll();

        return continents.stream()
                .collect(Collectors.toMap(
                        Continent::getContinentName,
                        continent -> continent.getEthnicities().stream()
                                .map(Ethnicity::getEthnicityName)
                                .sorted()
                                .collect(Collectors.toList())
                ));
    }

    private List<String> getGoalNames() {
        return goalRepository.findAll().stream()
                .map(Goal::getGoalName)
                .sorted()
                .collect(Collectors.toList());
    }

    private Map<String, GoalSymptomOptionsDTO> getSymptomsByGoal() {
        List<Goal> goals = goalRepository.findAll();

        return goals.stream()
                .collect(Collectors.toMap(
                        Goal::getGoalName,
                        goal -> {
                            List<String> symptoms = symptomRepository.findByGoal(goal).stream()
                                    .map(Symptom::getSymptomName)
                                    .sorted()
                                    .collect(Collectors.toList());

                            return GoalSymptomOptionsDTO.builder()
                                    .title(GOAL_SYMPTOM_TITLES.getOrDefault(
                                            goal.getGoalName(),
                                            "Which symptoms are you experiencing?"))
                                    .options(symptoms)
                                    .build();
                        }
                ));
    }

    private UserHealthDataDTO getUserHealthData(Integer userId, Integer version) {
        if (userId == null) {
            return null;
        }

        // If version is null, get current data; otherwise get data by version
        if (version == null) {
            return getCurrentUserHealthData(userId);
        } else {
            return getHistoricalUserHealthData(userId, version);  // Retrieve by VERSION
        }
    }

    private Map<String, List<String>> getTestRecommendationsByGoal() {
        log.debug("Fetching test recommendations grouped by goal");

        List<GoalTestRecommendationDTO> recommendations =
                testRecommendationRepository.findTestRecommendationsByGoal();

        return recommendations.stream()
                .collect(Collectors.groupingBy(
                        GoalTestRecommendationDTO::getGoalName,
                        Collectors.mapping(
                                GoalTestRecommendationDTO::getTestName,
                                Collectors.toList()
                        )
                ));
    }

    private UserHealthDataDTO getHistoricalUserHealthData(Integer userId, Integer version) {
        UserMeasurement measurement = userMeasurementRepository.findById_UserIdAndId_Version(userId, version)
                .orElse(null);

        UserEthnicity userEthnicity = userEthnicityRepository.findByUser_UserIdAndId_Version(userId, version)
                .stream()
                .findFirst()
                .orElse(null);

        List<UserGoal> userGoals = userGoalRepository.findByUser_UserIdAndId_Version(userId, version);

        LocalDate dateReported = userGoals.stream()
                .findFirst()
                .map(UserGoal::getDateReported)
                .orElse(null);

        List<String> goals = userGoals.stream()
                .map(ug -> mapper.mapGoalName(ug.getGoal()))
                .collect(Collectors.toList());

        Map<String, Integer> goalSeverities = userGoals.stream()
                .collect(Collectors.toMap(
                        ug -> mapper.mapGoalName(ug.getGoal()),
                        UserGoal::getSeverity
                ));

        List<UserSymptom> userSymptoms = userSymptomRepository.findByUser_UserIdAndId_Version(userId, version);
        Map<String, List<String>> symptomsByGoalName = userSymptoms.stream()
                .collect(Collectors.groupingBy(
                        us -> mapper.mapGoalName(us.getSymptom().getGoal()),
                        Collectors.mapping(
                                us -> mapper.mapSymptomName(us.getSymptom()),
                                Collectors.toList()
                        )
                ));

        List<String> recommendedLabs = userTestRecommendationRepository
                .findById_UserIdAndId_Version(userId, version)
                .stream()
                .map(utr -> utr.getTestRecommendation().getTestDefinition().getTestName())
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        return buildUserHealthDataDTO(measurement, userEthnicity, goals, goalSeverities,
                symptomsByGoalName, recommendedLabs, dateReported, version);
    }

    private UserHealthDataDTO getCurrentUserHealthData(Integer userId) {
        UserMeasurement measurement = userMeasurementRepository.findById_UserIdAndIsCurrentTrue(userId)
                .orElse(null);

        UserEthnicity userEthnicity = userEthnicityRepository.findByUser_UserIdAndIsCurrentTrue(userId)
                .stream()
                .findFirst()
                .orElse(null);

        List<UserGoal> userGoals = userGoalRepository.findByUser_UserIdAndIsCurrentTrue(userId);

        LocalDate dateReported = userGoals.stream()
                .findFirst()
                .map(UserGoal::getDateReported)
                .orElse(null);

        Integer version = userGoals.stream()
                .findFirst()
                .map(ug -> ug.getId().getVersion())
                .orElse(null);

        List<String> goals = userGoals.stream()
                .map(ug -> mapper.mapGoalName(ug.getGoal()))
                .collect(Collectors.toList());

        Map<String, Integer> goalSeverities = userGoals.stream()
                .collect(Collectors.toMap(
                        ug -> mapper.mapGoalName(ug.getGoal()),
                        UserGoal::getSeverity
                ));

        List<UserSymptom> userSymptoms = userSymptomRepository.findByUser_UserIdAndIsCurrentTrue(userId);
        Map<String, List<String>> symptomsByGoalName = userSymptoms.stream()
                .collect(Collectors.groupingBy(
                        us -> mapper.mapGoalName(us.getSymptom().getGoal()),
                        Collectors.mapping(
                                us -> mapper.mapSymptomName(us.getSymptom()),
                                Collectors.toList()
                        )
                ));

        List<String> recommendedLabs = userTestRecommendationRepository
                .findById_UserIdAndIsCurrentTrue(userId)
                .stream()
                .map(utr -> utr.getTestRecommendation().getTestDefinition().getTestName())
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        return buildUserHealthDataDTO(measurement, userEthnicity, goals, goalSeverities,
                symptomsByGoalName, recommendedLabs, dateReported, version);
    }

    private UserHealthDataDTO buildUserHealthDataDTO(
            UserMeasurement measurement,
            UserEthnicity userEthnicity,
            List<String> goals,
            Map<String, Integer> goalSeverities,
            Map<String, List<String>> symptomsByGoalName,
            List<String> recommendedLabs,  // Add this parameter
            LocalDate dateReported,
            Integer version) {

        return UserHealthDataDTO.builder()
                .height(measurement != null ? mapper.toHeightDTO(measurement) : null)
                .weight(measurement != null ? mapper.mapWeight(measurement) : null)
                .continent(userEthnicity != null ?
                        mapper.mapContinentName(userEthnicity.getEthnicity().getContinent()) : null)
                .ethnicity(userEthnicity != null ?
                        mapper.mapEthnicityName(userEthnicity.getEthnicity()) : null)
                .goals(goals)
                .goalSeverities(goalSeverities)
                .energyChallenges(symptomsByGoalName.getOrDefault("Energy", List.of()))
                .painTypes(symptomsByGoalName.getOrDefault("Pain management", List.of()))
                .sleepIssues(symptomsByGoalName.getOrDefault("Sleep", List.of()))
                .cognitiveIssues(symptomsByGoalName.getOrDefault("Cognitive health", List.of()))
                .fertilityIssues(symptomsByGoalName.getOrDefault("Fertility", List.of()))
                .digestionIssues(symptomsByGoalName.getOrDefault("Digestion", List.of()))
                .weightIssues(symptomsByGoalName.getOrDefault("Weight management", List.of()))
                .recommendedLabs(recommendedLabs)  // Add this
                .dateReported(dateReported)
                .version(version)
                .build();
    }
}