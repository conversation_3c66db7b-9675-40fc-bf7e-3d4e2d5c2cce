package com.noura.api.service.users;

import com.noura.api.dto.mapper.users.UserMapper;
import com.noura.api.dto.request.users.UpdateUserRequest;
import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.exception.custom.users.UserNotFoundException;
import com.noura.api.model.entity.User;
import com.noura.api.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Optional;


@Service
@RequiredArgsConstructor
@Slf4j
@Validated
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Transactional(readOnly = true)
    @Cacheable(value = "users", key = "#id")
    public UserResponse getUserById(Integer id) {
        log.debug("Fetching user by ID: {}", id);

        Optional<User> userOptional = userRepository.findById(id);
        if (userOptional.isEmpty()) {
            throw new RuntimeException("User not found with ID: " + id);
        }
        return userMapper.toResponse(userOptional.get());
    }

    @Transactional(readOnly = true)
    public Page<UserResponse> getAllUsers(Pageable pageable) {
        log.debug("Fetching all users with pagination");

        return userRepository.findAll(pageable)
                .map(userMapper::toResponse);
    }

    @Transactional(readOnly = true)
//    @Cacheable(value = "users", key = "#email")
    public UserResponse getUserByEmail(@Param("email") String email) {
        log.debug("Fetching user by email: {}", email);

        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isEmpty()) {
            throw new RuntimeException("User not found with email: " + email);
        }
        return userMapper.toResponse(userOptional.get());
    }

    public User findUserByLoginId(String loginId) {
        if (loginId == null || loginId.isEmpty()) {
            throw new IllegalArgumentException("The loginId input parameter cannot be null.");
        }

        log.debug("findUserByLoginId(\"{}\")", loginId);

        return userRepository.findByLoginId(loginId)
                .orElseThrow(() -> new UserNotFoundException("User not found with loginId: " + loginId));
    }

    @Transactional
    public UserResponse updateUserProfile(String email, UpdateUserRequest request) {
        log.info("Updating user profile for email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UserNotFoundException("User not found with email: " + email));

        // Update fields
        if (request.getPreferredCommMethod() != null) {
            user.setPreferredCommMethod(request.getPreferredCommMethod());
        }

        User updatedUser = userRepository.save(user);
        log.info("User profile updated successfully for email: {}", email);

        return userMapper.toUserResponse(updatedUser);
    }
}
